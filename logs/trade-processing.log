2025-08-20 10:33:02.713 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:43] - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 10:33:02.715 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:60] - 使用本地测试环境配置: localhost:9092
2025-08-20 10:33:02.757 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:89] - 应用本地测试Kafka配置
2025-08-20 10:33:02.808 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:247] - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 10:33:03.762 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 10:33:03.762 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:33:03.763 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 10:33:03.763 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:33:03.764 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:33:03.764 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:33:03.765 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:33:03.765 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:33:03.765 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 10:33:03.766 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 10:33:03.766 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 10:33:03.766 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 10:33:03.766 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 10:33:03.766 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 10:33:03.766 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 10:33:03.767 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 10:33:03.767 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 10:33:03.767 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:33:03.767 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 10:33:03.767 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 10:33:03.767 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:33:03.767 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 10:33:03.768 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:33:03.768 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 10:33:03.768 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:33:03.768 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 10:33:03.768 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:33:03.768 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 10:33:03.769 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:33:03.769 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 10:33:03.769 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 10:33:03.769 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:33:03.769 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 10:33:03.769 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 10:33:03.769 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 10:33:03.769 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:33:03.769 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 10:33:03.770 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 10:33:03.770 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 10:33:03.770 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:33:03.770 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 10:33:03.770 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 10:33:03.770 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 10:33:03.770 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 10:33:03.770 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:305] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:33:03.771 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 10:33:03.771 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 10:33:03.771 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 10:33:03.771 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 10:33:03.771 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 10:33:03.771 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 10:33:03.771 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:292] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 10:33:03.772 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 10:33:03.772 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 10:33:03.772 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:279] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 10:56:04.294 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:43] - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 10:56:04.297 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:60] - 使用本地测试环境配置: localhost:9092
2025-08-20 10:56:04.336 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:89] - 应用本地测试Kafka配置
2025-08-20 10:56:04.392 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:250] - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 10:56:05.319 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 10:56:05.319 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:56:05.321 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:56:05.321 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 10:56:05.322 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:56:05.322 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:56:05.323 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:56:05.323 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:56:05.323 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 10:56:05.324 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 10:56:05.324 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 10:56:05.324 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 10:56:05.324 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 10:56:05.324 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 10:56:05.325 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 10:56:05.325 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 10:56:05.325 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 10:56:05.325 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 10:56:05.325 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 10:56:05.325 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 10:56:05.326 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.326 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 10:56:05.326 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 10:56:05.326 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 10:56:05.326 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.326 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 10:56:05.327 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.327 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 10:56:05.327 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 10:56:05.327 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.327 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 10:56:05.327 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.328 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 10:56:05.328 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 10:56:05.328 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 10:56:05.328 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.328 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 10:56:05.328 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 10:56:05.329 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 10:56:05.329 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.329 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 10:56:05.329 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 10:56:05.329 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 10:56:05.329 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.329 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 10:56:05.330 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.330 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 10:56:05.330 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 10:56:05.330 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.330 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 10:56:05.330 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 10:56:05.331 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:56:05.331 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 10:56:05.331 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 10:56:05.331 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:56:05.331 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 10:56:05.331 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:56:05.331 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 10:56:05.332 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 10:56:05.332 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 10:56:05.332 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:56:05.332 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 10:56:05.332 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 10:56:05.332 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 10:56:05.332 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 10:56:05.332 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:56:05.332 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 10:56:05.333 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 10:56:05.333 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 10:56:05.333 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:56:05.333 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 10:56:05.333 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 10:56:05.333 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 10:56:05.333 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 10:56:05.334 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 10:56:05.334 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 10:56:05.334 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 10:56:05.334 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 10:56:05.334 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 10:56:05.334 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 10:56:05.334 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.334 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 10:56:05.335 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 10:56:05.335 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 10:56:05.335 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 10:56:05.335 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 10:56:05.335 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.335 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 10:56:05.335 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 10:56:05.335 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 10:56:05.336 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 10:56:05.336 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 10:56:05.336 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.336 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 10:56:05.336 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 10:56:05.336 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.337 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 10:56:05.337 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.337 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 10:56:05.337 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.337 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 10:56:05.337 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.337 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 10:56:05.338 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.338 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 10:56:05.338 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.338 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 10:56:05.338 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.338 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 10:56:05.338 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 10:56:05.338 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:56:05.339 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:56:05.339 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 10:56:05.339 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 10:56:05.339 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 10:56:05.339 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 10:56:05.339 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 10:56:05.339 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 10:56:05.339 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 10:56:05.340 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.340 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 10:56:05.340 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 10:56:05.340 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.340 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 10:56:05.340 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 10:56:05.340 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.341 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 10:56:05.341 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 10:56:05.341 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.341 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 10:56:05.341 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 10:56:05.341 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 10:56:05.341 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 10:56:05.341 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.341 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 10:56:05.342 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 10:56:05.342 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 10:56:05.342 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 10:56:05.342 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:56:05.342 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 10:56:05.342 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 10:56:05.342 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 10:56:05.342 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 10:56:05.342 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:56:05.342 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 10:56:05.343 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 10:56:05.343 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 10:56:05.343 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 10:56:05.343 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.343 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 10:56:05.343 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 10:56:05.343 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 10:56:05.343 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 10:56:05.343 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 10:56:05.343 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 10:56:05.344 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.344 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 10:56:05.344 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 10:56:05.344 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:56:05.344 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 10:56:05.344 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 10:56:05.344 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 10:56:05.344 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:56:05.344 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 10:56:05.344 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 10:56:05.345 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 10:56:05.345 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:56:05.345 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 10:56:05.345 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 10:56:05.345 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 10:56:05.345 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.345 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 10:56:05.345 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.346 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 10:56:05.346 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:56:05.346 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 10:56:05.346 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 10:56:05.346 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:56:05.346 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 10:56:05.346 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 10:56:05.346 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 10:56:05.347 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 10:56:05.347 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 10:56:05.347 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 10:56:05.347 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:56:05.347 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:56:05.347 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 10:56:05.347 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 10:56:05.347 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:56:05.347 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 10:56:05.347 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:56:05.348 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 10:56:05.348 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 10:56:05.348 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:56:05.348 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 10:56:05.348 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 10:56:05.348 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 10:56:05.348 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 10:56:05.348 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:56:05.348 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 10:56:05.348 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 10:56:05.349 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 10:56:05.349 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 10:56:05.349 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 10:56:05.349 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 10:56:05.349 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 10:56:05.349 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 10:56:05.349 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 10:56:05.349 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 10:56:05.349 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 10:56:05.349 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 10:56:05.350 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 10:56:05.350 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 10:56:05.350 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 10:56:05.350 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 10:56:05.350 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 10:56:05.350 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.351 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.351 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 10:56:05.351 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.351 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.351 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 10:56:05.351 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.351 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.352 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 10:56:05.352 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.352 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.352 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 10:56:05.352 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.352 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 10:56:05.352 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.353 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:56:05.353 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 10:56:05.353 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 10:56:05.353 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:56:05.353 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 10:56:05.353 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 10:56:05.353 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 10:56:05.354 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 10:56:05.354 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 10:56:05.354 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 10:56:05.354 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 10:56:05.354 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 10:56:05.354 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 10:56:05.354 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 10:56:05.355 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.355 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 10:56:05.355 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.355 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.355 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.355 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 10:56:05.356 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.356 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:56:05.356 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 10:56:05.356 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:56:05.356 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 10:56:05.356 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.356 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 10:56:05.356 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.357 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 10:56:05.357 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:56:05.357 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 10:56:05.357 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:56:05.357 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 10:56:05.357 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 10:56:05.357 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:56:05.357 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 10:56:05.357 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.357 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 10:56:05.358 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.358 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 10:56:05.358 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:56:05.358 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 10:56:05.358 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:56:05.358 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 10:56:05.358 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 10:56:05.358 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 10:56:05.358 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 10:56:05.358 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 10:56:05.358 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:56:05.359 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:56:05.359 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 10:56:05.359 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:56:05.359 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 10:56:05.359 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:56:05.359 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 10:56:05.359 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:56:05.359 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 10:56:05.359 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:56:05.359 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 10:56:05.360 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 10:56:05.360 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 10:56:05.360 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 10:56:05.360 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 10:56:05.360 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 10:56:05.360 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 10:56:05.360 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 10:56:05.360 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 10:56:05.360 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.360 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 10:56:05.360 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.361 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 10:56:05.361 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.361 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 10:56:05.361 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.361 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 10:56:05.361 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.361 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 10:56:05.361 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.361 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.361 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 10:56:05.362 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.362 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 10:56:05.362 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 10:56:05.362 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 10:56:05.362 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 10:56:05.362 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.362 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 10:56:05.363 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.363 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 10:56:05.363 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:56:05.363 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 10:56:05.363 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:56:05.363 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 10:56:05.363 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 10:56:05.363 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 10:56:05.363 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 10:56:05.363 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 10:56:05.363 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 10:56:05.364 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 10:56:05.364 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.364 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 10:56:05.364 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.364 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 10:56:05.364 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.364 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 10:56:05.364 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.364 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 10:56:05.364 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.364 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 10:56:05.365 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:56:05.365 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 10:56:05.365 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:56:05.365 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 10:56:05.365 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:56:05.365 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 10:56:05.365 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:56:05.365 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 10:56:05.365 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:56:05.365 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 10:56:05.365 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:56:05.365 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 10:56:05.366 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 10:56:05.366 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 10:56:05.366 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 10:56:05.366 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 10:56:05.366 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 10:56:05.366 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 10:56:05.366 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 10:56:05.366 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 10:56:05.366 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.366 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 10:56:05.366 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.366 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 10:56:05.367 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.367 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 10:56:05.367 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 10:56:05.367 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.367 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 10:56:05.367 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 10:56:05.367 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.367 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.367 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 10:56:05.368 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:56:05.368 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:56:05.368 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:56:05.368 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 10:56:05.368 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:56:05.368 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 10:56:05.369 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 10:56:05.369 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 10:56:05.369 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 10:56:05.369 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 10:56:05.369 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 10:56:05.370 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 10:56:05.370 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 10:56:05.370 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 10:56:05.371 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 10:56:05.371 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 10:56:05.371 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 10:56:05.371 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 10:56:05.371 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 10:56:05.372 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 10:56:05.372 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 10:56:05.372 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 10:56:05.372 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 10:56:05.372 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 10:56:05.373 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 10:56:05.373 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 10:56:05.373 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 10:56:05.373 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 10:56:05.373 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 10:56:05.373 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 10:59:50.186 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:43] - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 10:59:50.189 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:60] - 使用本地测试环境配置: localhost:9092
2025-08-20 10:59:50.235 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:89] - 应用本地测试Kafka配置
2025-08-20 10:59:50.287 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:250] - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 10:59:51.190 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:59:51.190 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 10:59:51.192 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:59:51.193 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 10:59:51.193 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:59:51.194 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:59:51.195 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:59:51.195 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 10:59:51.195 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:59:51.195 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 10:59:51.195 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 10:59:51.196 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 10:59:51.196 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 10:59:51.196 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 10:59:51.196 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 10:59:51.196 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 10:59:51.196 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 10:59:51.197 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 10:59:51.197 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 10:59:51.197 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 10:59:51.197 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 10:59:51.197 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.197 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 10:59:51.198 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 10:59:51.198 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.198 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.198 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 10:59:51.198 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 10:59:51.198 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.198 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 10:59:51.198 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.199 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 10:59:51.199 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 10:59:51.199 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.199 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 10:59:51.199 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 10:59:51.199 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.199 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 10:59:51.199 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.199 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 10:59:51.200 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 10:59:51.200 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.200 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 10:59:51.200 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 10:59:51.200 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.200 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 10:59:51.200 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 10:59:51.200 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:59:51.200 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 10:59:51.200 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 10:59:51.201 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 10:59:51.201 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 10:59:51.201 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:59:51.201 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 10:59:51.201 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 10:59:51.201 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 10:59:51.201 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:59:51.201 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 10:59:51.202 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 10:59:51.202 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:59:51.202 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 10:59:51.202 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:59:51.202 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 10:59:51.202 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:59:51.202 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 10:59:51.203 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 10:59:51.203 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 10:59:51.203 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 10:59:51.203 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 10:59:51.203 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 10:59:51.203 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 10:59:51.203 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 10:59:51.203 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 10:59:51.203 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 10:59:51.203 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 10:59:51.203 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 10:59:51.203 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 10:59:51.204 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.204 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 10:59:51.204 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 10:59:51.204 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.204 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 10:59:51.204 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 10:59:51.204 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.204 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 10:59:51.204 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 10:59:51.204 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.204 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 10:59:51.204 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 10:59:51.205 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.205 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 10:59:51.205 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 10:59:51.205 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.205 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 10:59:51.205 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 10:59:51.205 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.205 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 10:59:51.205 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 10:59:51.205 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.206 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 10:59:51.206 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.206 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 10:59:51.206 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.206 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 10:59:51.206 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 10:59:51.206 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:59:51.206 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:59:51.207 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 10:59:51.207 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 10:59:51.207 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 10:59:51.207 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 10:59:51.207 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 10:59:51.207 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 10:59:51.207 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 10:59:51.207 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 10:59:51.208 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 10:59:51.208 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.208 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 10:59:51.208 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 10:59:51.208 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 10:59:51.209 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.209 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 10:59:51.209 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 10:59:51.209 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.209 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 10:59:51.209 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.209 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 10:59:51.210 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.210 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 10:59:51.210 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 10:59:51.210 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:59:51.210 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 10:59:51.210 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:59:51.210 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 10:59:51.210 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 10:59:51.210 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.211 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 10:59:51.211 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.211 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 10:59:51.211 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 10:59:51.211 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:59:51.211 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:59:51.211 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 10:59:51.211 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 10:59:51.211 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 10:59:51.211 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 10:59:51.212 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:59:51.212 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 10:59:51.212 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 10:59:51.212 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.212 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 10:59:51.212 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.212 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 10:59:51.212 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 10:59:51.212 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:59:51.212 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 10:59:51.213 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:59:51.213 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 10:59:51.213 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 10:59:51.213 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 10:59:51.213 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 10:59:51.213 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 10:59:51.213 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 10:59:51.213 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 10:59:51.214 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 10:59:51.214 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 10:59:51.214 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:59:51.214 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 10:59:51.214 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 10:59:51.214 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 10:59:51.214 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:59:51.214 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 10:59:51.214 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 10:59:51.215 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:59:51.215 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 10:59:51.215 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 10:59:51.215 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 10:59:51.215 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:59:51.215 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 10:59:51.215 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 10:59:51.215 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:59:51.216 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 10:59:51.216 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 10:59:51.216 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 10:59:51.216 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:59:51.216 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 10:59:51.217 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 10:59:51.217 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 10:59:51.217 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 10:59:51.217 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 10:59:51.217 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 10:59:51.217 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 10:59:51.217 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 10:59:51.217 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 10:59:51.218 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 10:59:51.218 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 10:59:51.218 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 10:59:51.218 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.218 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.218 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 10:59:51.218 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 10:59:51.218 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 10:59:51.218 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.218 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 10:59:51.219 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.219 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 10:59:51.219 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 10:59:51.219 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.219 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 10:59:51.219 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 10:59:51.219 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.219 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 10:59:51.219 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 10:59:51.219 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.219 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 10:59:51.219 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.219 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 10:59:51.219 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 10:59:51.220 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.220 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 10:59:51.220 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 10:59:51.220 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.220 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 10:59:51.220 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 10:59:51.220 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 10:59:51.220 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:59:51.220 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 10:59:51.220 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 10:59:51.221 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:59:51.221 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 10:59:51.221 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 10:59:51.221 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 10:59:51.221 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 10:59:51.221 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 10:59:51.221 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 10:59:51.222 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 10:59:51.222 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 10:59:51.222 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 10:59:51.222 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 10:59:51.222 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 10:59:51.222 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 10:59:51.222 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 10:59:51.222 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.222 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 10:59:51.223 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 10:59:51.223 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 10:59:51.223 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.223 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 10:59:51.223 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.223 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 10:59:51.223 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 10:59:51.223 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.223 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.224 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 10:59:51.224 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 10:59:51.224 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 10:59:51.224 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 10:59:51.224 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:59:51.224 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:59:51.224 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 10:59:51.224 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 10:59:51.225 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.225 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.225 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 10:59:51.225 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 10:59:51.225 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:59:51.225 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 10:59:51.225 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 10:59:51.225 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:59:51.226 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 10:59:51.226 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 10:59:51.226 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 10:59:51.226 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:59:51.226 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 10:59:51.226 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 10:59:51.226 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.226 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 10:59:51.226 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.226 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 10:59:51.226 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 10:59:51.226 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:59:51.227 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 10:59:51.227 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:59:51.227 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 10:59:51.227 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 10:59:51.227 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 10:59:51.227 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 10:59:51.227 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 10:59:51.227 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 10:59:51.227 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 10:59:51.227 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:59:51.227 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 10:59:51.228 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:59:51.228 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 10:59:51.228 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 10:59:51.228 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:59:51.228 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 10:59:51.228 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:59:51.228 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 10:59:51.228 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:59:51.228 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 10:59:51.228 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:59:51.228 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 10:59:51.229 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 10:59:51.229 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 10:59:51.229 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 10:59:51.229 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 10:59:51.229 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 10:59:51.229 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 10:59:51.230 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 10:59:51.230 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.230 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 10:59:51.230 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.230 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.230 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 10:59:51.230 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.230 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 10:59:51.231 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.231 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 10:59:51.231 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 10:59:51.231 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.231 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 10:59:51.231 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.231 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 10:59:51.232 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.232 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.232 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 10:59:51.232 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 10:59:51.232 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.232 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 10:59:51.232 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 10:59:51.232 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 10:59:51.233 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 10:59:51.233 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 10:59:51.233 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 10:59:51.233 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 10:59:51.233 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 10:59:51.233 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 10:59:51.233 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 10:59:51.233 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.233 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 10:59:51.233 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.233 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 10:59:51.234 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.234 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 10:59:51.234 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.234 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 10:59:51.234 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.234 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 10:59:51.234 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:59:51.234 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 10:59:51.234 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:59:51.234 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 10:59:51.234 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:59:51.235 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:59:51.235 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 10:59:51.235 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:59:51.235 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 10:59:51.235 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:59:51.235 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 10:59:51.235 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 10:59:51.235 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 10:59:51.235 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 10:59:51.235 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 10:59:51.236 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 10:59:51.236 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 10:59:51.236 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 10:59:51.236 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 10:59:51.236 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.236 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 10:59:51.236 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.236 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.236 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 10:59:51.236 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.236 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 10:59:51.237 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.237 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 10:59:51.237 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.237 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 10:59:51.237 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.237 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 10:59:51.237 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 10:59:51.237 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 10:59:51.237 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 10:59:51.237 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 10:59:51.237 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 10:59:51.238 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 10:59:51.238 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:59:51.238 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 10:59:51.238 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:59:51.238 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 10:59:51.238 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 10:59:51.238 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 10:59:51.238 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 10:59:51.238 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 10:59:51.238 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:59:51.238 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 10:59:51.238 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 10:59:51.239 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 10:59:51.239 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 10:59:51.239 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 10:59:51.239 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 10:59:51.239 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 10:59:51.239 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 10:59:51.239 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 10:59:51.239 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 10:59:51.239 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 10:59:51.239 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.239 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 10:59:51.240 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.240 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 10:59:51.240 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.240 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 10:59:51.240 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.240 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 10:59:51.240 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 10:59:51.240 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 10:59:51.241 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 10:59:51.241 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 10:59:51.241 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 10:59:51.241 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 10:59:51.241 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 10:59:51.241 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 10:59:51.242 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 10:59:51.242 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 10:59:51.242 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 10:59:51.242 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 10:59:51.242 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 10:59:51.243 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 10:59:51.243 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 10:59:51.243 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 10:59:51.243 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 10:59:51.243 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 10:59:51.244 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 10:59:51.244 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 10:59:51.244 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 10:59:51.244 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 10:59:51.244 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 10:59:51.245 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 10:59:51.245 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 10:59:51.245 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 10:59:51.245 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 10:59:51.245 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 10:59:51.245 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 10:59:51.245 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 10:59:51.246 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 10:59:51.246 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 10:59:51.246 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:00:45.854 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:00:45.969 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:00:46.085 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:00:46.200 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:00:46.316 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:00:46.434 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:00:46.548 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:00:46.668 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:00:46.784 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:00:46.900 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:00:47.016 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:00:47.128 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:00:47.249 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:00:47.368 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:00:47.482 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:00:47.596 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:00:47.716 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:00:47.835 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:00:47.952 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:00:48.070 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:00:48.186 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 11:00:48.301 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 11:00:48.417 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 11:00:48.537 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 11:00:48.650 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 11:00:48.771 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 11:00:48.887 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 11:00:49.005 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 11:00:49.122 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 11:00:49.240 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 11:00:49.354 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 11:00:49.470 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 11:00:49.582 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 11:00:49.701 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 11:00:49.820 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 11:00:49.934 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:00:50.052 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:00:50.172 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 11:00:50.290 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 11:00:50.407 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 11:00:50.525 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 11:00:50.641 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 11:00:50.757 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 11:00:50.874 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 11:00:50.988 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 11:00:51.104 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 11:00:51.218 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 11:00:51.333 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 11:00:51.449 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 11:00:51.567 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 11:00:51.685 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257500, 状态=3, 剩余量=5
2025-08-20 11:00:51.802 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257500, 状态=1, 剩余量=3
2025-08-20 11:00:51.919 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257560, 状态=3, 剩余量=5
2025-08-20 11:00:52.037 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257560, 状态=1, 剩余量=2
2025-08-20 11:01:28.173 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:43] - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 11:01:28.175 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:60] - 使用本地测试环境配置: localhost:9092
2025-08-20 11:01:28.216 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:89] - 应用本地测试Kafka配置
2025-08-20 11:01:28.268 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:250] - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 11:01:29.155 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:01:29.155 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.156 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.156 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:01:29.157 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.157 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.157 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.158 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.158 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:01:29.158 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:01:29.159 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:01:29.159 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:01:29.159 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:01:29.159 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:01:29.159 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:01:29.159 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:01:29.159 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:01:29.159 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.160 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:01:29.160 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:01:29.160 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.160 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:01:29.160 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:01:29.160 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.160 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:01:29.160 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.160 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:01:29.161 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.161 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:01:29.161 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:01:29.161 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.161 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:01:29.161 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.161 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:01:29.161 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:01:29.162 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.162 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:01:29.162 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:01:29.162 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.162 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:01:29.162 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.162 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:01:29.162 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:01:29.163 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.163 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:01:29.163 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.163 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:01:29.163 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.163 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:01:29.163 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:01:29.164 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.164 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:01:29.164 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:01:29.164 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.164 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:01:29.164 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:01:29.164 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.164 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:01:29.164 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:01:29.164 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:01:29.164 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:01:29.165 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:01:29.165 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:01:29.165 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:01:29.165 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:01:29.165 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:01:29.165 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:01:29.165 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:01:29.165 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:01:29.165 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:01:29.165 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:01:29.165 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:01:29.166 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:01:29.166 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:01:29.166 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.166 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:01:29.166 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:01:29.166 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.166 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:01:29.166 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:01:29.167 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.167 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:01:29.167 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:01:29.167 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.167 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:01:29.167 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:01:29.167 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.167 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:01:29.167 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.167 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:01:29.167 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:01:29.168 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.168 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:01:29.168 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:01:29.168 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.168 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:01:29.168 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.168 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:01:29.168 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:01:29.168 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.168 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:01:29.169 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:01:29.169 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:01:29.169 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.169 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:01:29.169 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:01:29.169 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:01:29.169 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:01:29.169 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.169 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:01:29.169 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:01:29.170 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:01:29.170 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:01:29.170 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 11:01:29.170 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:01:29.170 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:01:29.170 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 11:01:29.170 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:01:29.170 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.170 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:01:29.171 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.171 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:01:29.171 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:01:29.171 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.171 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:01:29.171 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:01:29.171 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.171 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:01:29.171 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.172 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:01:29.172 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:01:29.172 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.172 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:01:29.172 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.172 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:01:29.172 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:01:29.172 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.172 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 11:01:29.172 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:01:29.173 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.173 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 11:01:29.173 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:01:29.173 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 11:01:29.173 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:01:29.173 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.173 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 11:01:29.173 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:01:29.173 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 11:01:29.173 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:01:29.174 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.174 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 11:01:29.174 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:01:29.174 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 11:01:29.174 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:01:29.174 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 11:01:29.174 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 11:01:29.174 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.174 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.175 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.175 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 11:01:29.175 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:01:29.175 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.175 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 11:01:29.175 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.175 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:01:29.175 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 11:01:29.175 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:01:29.176 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 11:01:29.176 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 11:01:29.176 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 11:01:29.176 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.176 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:01:29.176 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 11:01:29.176 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.176 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:01:29.176 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.176 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 11:01:29.176 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:01:29.177 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.177 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:01:29.177 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:01:29.177 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.177 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:01:29.177 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.177 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:01:29.177 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 11:01:29.177 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:01:29.177 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 11:01:29.177 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:01:29.178 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:01:29.178 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 11:01:29.178 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:01:29.178 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:01:29.178 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 11:01:29.178 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:01:29.178 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 11:01:29.178 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:01:29.178 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:01:29.178 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 11:01:29.178 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:01:29.179 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:01:29.179 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 11:01:29.179 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:01:29.179 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 11:01:29.179 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.179 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:01:29.179 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 11:01:29.179 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:01:29.179 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.179 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 11:01:29.179 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:01:29.180 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 11:01:29.180 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:01:29.180 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.180 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 11:01:29.180 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:01:29.180 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.180 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 11:01:29.180 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:01:29.180 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.180 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:01:29.180 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:01:29.181 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:01:29.181 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:01:29.181 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.181 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:01:29.181 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:01:29.181 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.181 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:01:29.181 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:01:29.181 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.181 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:01:29.181 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:01:29.182 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:01:29.182 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.182 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.182 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:01:29.182 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:01:29.182 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.182 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:01:29.182 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:01:29.182 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.182 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:01:29.182 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:01:29.183 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:01:29.183 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:01:29.183 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 11:01:29.183 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:01:29.183 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:01:29.183 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 11:01:29.183 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:01:29.183 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.183 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:01:29.183 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:01:29.184 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.184 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:01:29.184 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.184 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:01:29.184 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:01:29.184 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.184 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:01:29.184 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.185 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:01:29.185 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:01:29.185 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.185 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:01:29.185 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.185 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:01:29.185 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:01:29.185 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.185 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:01:29.185 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:01:29.185 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.185 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:01:29.185 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:01:29.185 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.186 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:01:29.186 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:01:29.186 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:01:29.186 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:01:29.186 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.186 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 11:01:29.186 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:01:29.186 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 11:01:29.186 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:01:29.186 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 11:01:29.186 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 11:01:29.186 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:01:29.187 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 11:01:29.187 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:01:29.187 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 11:01:29.187 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.187 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:01:29.187 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 11:01:29.187 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:01:29.187 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 11:01:29.187 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.187 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:01:29.187 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 11:01:29.187 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:01:29.187 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 11:01:29.187 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.188 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:01:29.188 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 11:01:29.188 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:01:29.188 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 11:01:29.188 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.188 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:01:29.188 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 11:01:29.188 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:01:29.188 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.188 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 11:01:29.188 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:01:29.189 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 11:01:29.189 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:01:29.189 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:01:29.189 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 11:01:29.189 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 11:01:29.189 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:01:29.189 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.189 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:01:29.189 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.189 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 11:01:29.189 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.189 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 11:01:29.189 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.190 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 11:01:29.190 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.190 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 11:01:29.190 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.190 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 11:01:29.190 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 11:01:29.190 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:01:29.190 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 11:01:29.190 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:01:29.191 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 11:01:29.191 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:01:29.191 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 11:01:29.191 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:01:29.191 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 11:01:29.191 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.191 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 11:01:29.191 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.191 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 11:01:29.191 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.191 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 11:01:29.192 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.192 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:01:29.192 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.192 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:01:29.192 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.192 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:01:29.192 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.192 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:01:29.192 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.192 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:01:29.192 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.192 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:01:29.193 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.193 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:01:29.193 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.193 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:01:29.193 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.193 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:01:29.193 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:01:29.193 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:01:29.193 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 11:01:29.193 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:01:29.193 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 11:01:29.193 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:01:29.194 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.194 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:01:29.194 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.194 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:01:29.194 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.194 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:01:29.194 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.194 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:01:29.194 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.194 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:01:29.194 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.195 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:01:29.195 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.195 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:01:29.195 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.195 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:01:29.195 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.195 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 11:01:29.195 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.195 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 11:01:29.195 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.195 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 11:01:29.195 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:01:29.196 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 11:01:29.196 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:01:29.196 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 11:01:29.196 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:01:29.196 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 11:01:29.196 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:01:29.196 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 11:01:29.196 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.196 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 11:01:29.196 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.196 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 11:01:29.196 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.197 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.197 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 11:01:29.197 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.197 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:01:29.197 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.197 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:01:29.197 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.197 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:01:29.198 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:01:29.198 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:01:29.198 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.198 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:01:29.198 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.198 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:01:29.198 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.198 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:01:29.198 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.198 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:01:29.198 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.199 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:01:29.199 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.199 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:01:29.199 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.199 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:01:29.199 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.199 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:01:29.199 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.199 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:01:29.199 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:01:29.199 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:01:29.199 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:01:29.200 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:01:29.200 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:01:29.200 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:01:29.200 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:01:29.200 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.200 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:01:29.200 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.200 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:01:29.200 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.200 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:01:29.200 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.200 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:01:29.200 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.201 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:01:29.201 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.201 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:01:29.201 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.201 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:01:29.201 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:01:29.201 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:01:29.201 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:01:29.201 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:01:29.201 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.201 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:01:29.201 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:01:29.201 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:01:29.202 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:01:29.202 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:01:29.202 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:01:29.202 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:01:29.202 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:01:29.202 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:01:29.202 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:01:29.202 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:01:29.202 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.202 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.202 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:01:29.202 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.202 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:01:29.203 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.203 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:01:29.203 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.203 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:01:29.203 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.203 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:01:29.203 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:01:29.203 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:01:29.203 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:01:29.203 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.203 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:01:29.203 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:01:29.203 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.204 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:01:29.204 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.204 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:01:29.204 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.204 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:01:29.204 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.204 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:01:29.204 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:01:29.204 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:01:29.204 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 11:01:29.204 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:01:29.204 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 11:01:29.205 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:01:29.205 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:01:29.205 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:01:29.205 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.205 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:01:29.205 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.205 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:01:29.205 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.205 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:01:29.205 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.205 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:01:29.205 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.207 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.207 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:01:29.207 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.207 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:01:29.207 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.207 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 11:01:29.207 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.207 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 11:01:29.207 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.207 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 11:01:29.207 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 11:01:29.208 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 11:01:29.208 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.208 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 11:01:29.208 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:01:29.208 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 11:01:29.208 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:01:29.208 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 11:01:29.208 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:01:29.208 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:01:29.208 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 11:01:29.208 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:01:29.208 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 11:01:29.208 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 11:01:29.209 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 11:01:29.209 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 11:01:29.209 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 11:01:29.209 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 11:01:29.209 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 11:01:29.209 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 11:01:29.209 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:01:29.210 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:01:29.210 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 11:01:29.210 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 11:01:29.210 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 11:01:29.210 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 11:01:29.210 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 11:01:29.211 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 11:01:29.211 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 11:01:29.211 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 11:01:29.211 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 11:01:29.211 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 11:01:29.211 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 11:01:29.211 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 11:01:29.212 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 11:01:29.212 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257500, 状态=3, 剩余量=5
2025-08-20 11:01:29.212 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257500, 状态=1, 剩余量=3
2025-08-20 11:01:29.212 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257560, 状态=3, 剩余量=5
2025-08-20 11:01:29.212 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257560, 状态=1, 剩余量=2
2025-08-20 11:01:29.212 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257560, 状态=0, 剩余量=0
2025-08-20 11:01:29.212 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257500, 状态=1, 剩余量=1
2025-08-20 11:01:29.213 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257500, 状态=0, 剩余量=0
2025-08-20 11:01:29.213 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257608, 状态=3, 剩余量=1
2025-08-20 11:01:29.213 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257608, 状态=0, 剩余量=0
2025-08-20 11:01:29.213 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257648, 状态=3, 剩余量=2
2025-08-20 11:01:29.213 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257648, 状态=1, 剩余量=1
2025-08-20 11:01:29.213 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257648, 状态=0, 剩余量=0
2025-08-20 11:01:29.213 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257736, 状态=3, 剩余量=2
2025-08-20 11:01:29.214 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257736, 状态=5, 剩余量=2
2025-08-20 11:01:29.214 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257414, 状态=3, 剩余量=10
2025-08-20 11:01:29.214 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257414, 状态=0, 剩余量=0
2025-08-20 11:01:29.214 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257342, 状态=3, 剩余量=10
2025-08-20 11:01:29.214 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257342, 状态=0, 剩余量=0
2025-08-20 11:01:29.214 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100258082, 状态=3, 剩余量=2
2025-08-20 11:01:29.214 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100258082, 状态=5, 剩余量=2
2025-08-20 11:01:29.215 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100258081, 状态=3, 剩余量=2
2025-08-20 11:01:29.215 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100258083, 状态=3, 剩余量=2
2025-08-20 11:01:29.215 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257349, 状态=3, 剩余量=1
2025-08-20 11:02:24.058 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:43] - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 11:02:24.064 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:60] - 使用本地测试环境配置: localhost:9092
2025-08-20 11:02:24.104 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:89] - 应用本地测试Kafka配置
2025-08-20 11:02:24.153 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:250] - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 11:02:25.054 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:02:25.054 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.056 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.056 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:02:25.056 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.056 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.057 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.057 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.057 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:02:25.058 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:02:25.058 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:02:25.058 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:02:25.058 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:02:25.059 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:02:25.059 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.059 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:02:25.059 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:02:25.059 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.059 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:02:25.059 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.059 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:02:25.060 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:02:25.060 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.060 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:02:25.060 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:02:25.060 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.060 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:02:25.060 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.060 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:02:25.061 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:02:25.061 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:02:25.061 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.061 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:02:25.061 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.061 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:02:25.061 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.061 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:02:25.061 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:02:25.061 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.061 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:02:25.062 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:02:25.062 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.062 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:02:25.062 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:02:25.062 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.062 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:02:25.062 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:02:25.062 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.062 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:02:25.062 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.063 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:02:25.063 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:02:25.063 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.063 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:02:25.063 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.063 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:02:25.063 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:02:25.063 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:02:25.064 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:02:25.064 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:02:25.064 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:02:25.064 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:02:25.064 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:02:25.064 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:02:25.064 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:02:25.064 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:02:25.064 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:02:25.065 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.065 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:02:25.065 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.065 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:02:25.065 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:02:25.065 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.065 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.065 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:02:25.065 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:02:25.066 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.066 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:02:25.066 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:02:25.066 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.066 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:02:25.066 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:02:25.066 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.066 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:02:25.066 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:02:25.066 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.066 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:02:25.067 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.067 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:02:25.067 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:02:25.067 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.067 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:02:25.067 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:02:25.067 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:02:25.067 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:02:25.067 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.067 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:02:25.067 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:02:25.068 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:02:25.068 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:02:25.068 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.068 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:02:25.068 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:02:25.068 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:02:25.068 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:02:25.068 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:02:25.068 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:02:25.068 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:02:25.069 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 11:02:25.069 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:02:25.069 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:02:25.069 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 11:02:25.069 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:02:25.069 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:02:25.069 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:02:25.069 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:02:25.069 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.069 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:02:25.070 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:02:25.070 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:02:25.070 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.070 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:02:25.070 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:02:25.070 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.070 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:02:25.070 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:02:25.070 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.071 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:02:25.071 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 11:02:25.071 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.071 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:02:25.071 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 11:02:25.071 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:02:25.071 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 11:02:25.071 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.071 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 11:02:25.071 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.071 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:02:25.072 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.072 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 11:02:25.072 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:02:25.072 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.072 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:02:25.072 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 11:02:25.072 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.072 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:02:25.072 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.072 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 11:02:25.072 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:02:25.073 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 11:02:25.073 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 11:02:25.073 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:02:25.073 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.073 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 11:02:25.073 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:02:25.073 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.073 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 11:02:25.073 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:02:25.073 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.073 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 11:02:25.073 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.074 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 11:02:25.074 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.074 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:02:25.074 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 11:02:25.074 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:02:25.074 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 11:02:25.074 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 11:02:25.074 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:02:25.074 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.074 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 11:02:25.075 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.075 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:02:25.075 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:02:25.075 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.075 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:02:25.075 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.075 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 11:02:25.075 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.075 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:02:25.075 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 11:02:25.075 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.076 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:02:25.076 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 11:02:25.076 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:02:25.076 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:02:25.076 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:02:25.076 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 11:02:25.076 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:02:25.076 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:02:25.076 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 11:02:25.077 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:02:25.077 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 11:02:25.077 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:02:25.077 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:02:25.077 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 11:02:25.077 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.077 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:02:25.077 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 11:02:25.077 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.077 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:02:25.077 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 11:02:25.077 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.077 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:02:25.078 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 11:02:25.078 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.078 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:02:25.078 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 11:02:25.078 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:02:25.078 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.078 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 11:02:25.078 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:02:25.078 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.078 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 11:02:25.078 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:02:25.078 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.078 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:02:25.078 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:02:25.079 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.079 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:02:25.079 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:02:25.079 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.079 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:02:25.079 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:02:25.079 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.079 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:02:25.079 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:02:25.079 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.079 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:02:25.079 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:02:25.079 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.079 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:02:25.079 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:02:25.080 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:02:25.080 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:02:25.080 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:02:25.080 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 11:02:25.080 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:02:25.080 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 11:02:25.080 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:02:25.080 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:02:25.080 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.080 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:02:25.081 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.081 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:02:25.081 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.081 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:02:25.081 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:02:25.081 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.081 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:02:25.081 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.081 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:02:25.081 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:02:25.081 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.082 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:02:25.082 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:02:25.082 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:02:25.082 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.082 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:02:25.082 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:02:25.082 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:02:25.082 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:02:25.082 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.083 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:02:25.083 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.083 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:02:25.083 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:02:25.083 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.083 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:02:25.083 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:02:25.083 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:02:25.083 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:02:25.083 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.083 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 11:02:25.084 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:02:25.084 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 11:02:25.084 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 11:02:25.084 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:02:25.084 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 11:02:25.084 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.084 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:02:25.084 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 11:02:25.084 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.084 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:02:25.084 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 11:02:25.084 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.084 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:02:25.084 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 11:02:25.084 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.085 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:02:25.085 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 11:02:25.085 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.085 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:02:25.085 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 11:02:25.085 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:02:25.085 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:02:25.085 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 11:02:25.085 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 11:02:25.085 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:02:25.085 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.085 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 11:02:25.085 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:02:25.085 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.085 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 11:02:25.085 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:02:25.086 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.086 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 11:02:25.086 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:02:25.086 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.086 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 11:02:25.086 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:02:25.086 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.086 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 11:02:25.086 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:02:25.086 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.086 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 11:02:25.086 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:02:25.086 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:02:25.086 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:02:25.086 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:02:25.087 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:02:25.087 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:02:25.087 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:02:25.087 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:02:25.087 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:02:25.087 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 11:02:25.087 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:02:25.087 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:02:25.087 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 11:02:25.087 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.087 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:02:25.087 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 11:02:25.087 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.087 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:02:25.087 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 11:02:25.087 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.088 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:02:25.088 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 11:02:25.088 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.088 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 11:02:25.088 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.088 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 11:02:25.088 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.088 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 11:02:25.088 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.088 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 11:02:25.088 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.089 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 11:02:25.089 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.089 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 11:02:25.089 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.089 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 11:02:25.089 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.089 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.089 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 11:02:25.089 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:02:25.089 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:02:25.090 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 11:02:25.090 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:02:25.090 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 11:02:25.090 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:02:25.090 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.090 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:02:25.090 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.090 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:02:25.090 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:02:25.090 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.090 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:02:25.091 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.091 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:02:25.091 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.091 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:02:25.091 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.091 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:02:25.091 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.092 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:02:25.092 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.092 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:02:25.092 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.092 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:02:25.092 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.092 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.092 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:02:25.092 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:02:25.092 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:02:25.093 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:02:25.093 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:02:25.093 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:02:25.093 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:02:25.093 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:02:25.093 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:02:25.093 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.093 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:02:25.093 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.093 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.093 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:02:25.094 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.094 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 11:02:25.094 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.094 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 11:02:25.094 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.094 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 11:02:25.094 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.094 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 11:02:25.094 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.094 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.094 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 11:02:25.095 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.095 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 11:02:25.095 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.095 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 11:02:25.095 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.095 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 11:02:25.095 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.095 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 11:02:25.095 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.095 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 11:02:25.095 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.096 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:02:25.096 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.096 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:02:25.096 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:02:25.096 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:02:25.096 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:02:25.096 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:02:25.096 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:02:25.096 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:02:25.096 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:02:25.096 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.097 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.097 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:02:25.097 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.097 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:02:25.097 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.097 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:02:25.097 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.097 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:02:25.098 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.098 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:02:25.098 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.098 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:02:25.098 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:02:25.098 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:02:25.098 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:02:25.098 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:02:25.098 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.098 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:02:25.099 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:02:25.099 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:02:25.099 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:02:25.099 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:02:25.099 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:02:25.099 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:02:25.099 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:02:25.099 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:02:25.099 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:02:25.099 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:02:25.099 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:02:25.100 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.100 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:02:25.100 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.100 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:02:25.100 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.100 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:02:25.100 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.100 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:02:25.100 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.100 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:02:25.100 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.100 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:02:25.101 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:02:25.101 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:02:25.101 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.101 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:02:25.101 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.101 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:02:25.101 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:02:25.101 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.101 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:02:25.101 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.101 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:02:25.101 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.102 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:02:25.102 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:02:25.102 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:02:25.102 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 11:02:25.102 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:02:25.102 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 11:02:25.102 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:02:25.102 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:02:25.102 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:02:25.102 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.102 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:02:25.103 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.103 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:02:25.103 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:02:25.103 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.103 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:02:25.103 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.103 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:02:25.103 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.103 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:02:25.103 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.103 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.103 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:02:25.104 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:02:25.104 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.104 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:02:25.104 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.104 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:02:25.104 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.104 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:02:25.104 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 11:02:25.104 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.104 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:02:25.104 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:02:25.104 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:02:25.105 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:02:25.105 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:02:25.105 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 11:02:25.105 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:02:25.105 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 11:02:25.105 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:02:25.105 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 11:02:25.105 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:02:25.105 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 11:02:25.105 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 11:02:25.105 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 11:02:25.106 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 11:02:25.106 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 11:02:25.106 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 11:02:25.106 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 11:02:25.106 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 11:02:25.106 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 11:02:25.106 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 11:02:25.107 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 11:02:25.107 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 11:02:25.107 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 11:02:25.107 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 11:02:25.107 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 11:02:25.107 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 11:02:25.107 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 11:02:25.108 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:02:25.108 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 11:02:25.108 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 11:02:25.108 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 11:02:25.108 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 11:02:25.108 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 11:02:25.108 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 11:02:25.109 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 11:02:25.109 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 11:02:25.109 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 11:02:25.109 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 11:02:25.109 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 11:02:25.109 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 11:02:25.109 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 11:02:25.109 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 11:02:25.110 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257500, 状态=3, 剩余量=5
2025-08-20 11:02:25.110 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257500, 状态=1, 剩余量=3
2025-08-20 11:02:25.110 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257560, 状态=3, 剩余量=5
2025-08-20 11:02:25.110 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257560, 状态=1, 剩余量=2
2025-08-20 11:02:25.110 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257560, 状态=0, 剩余量=0
2025-08-20 11:02:25.111 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257500, 状态=1, 剩余量=1
2025-08-20 11:02:25.111 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257500, 状态=0, 剩余量=0
2025-08-20 11:02:25.111 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257608, 状态=3, 剩余量=1
2025-08-20 11:02:25.111 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257608, 状态=0, 剩余量=0
2025-08-20 11:02:25.111 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257648, 状态=3, 剩余量=2
2025-08-20 11:02:25.111 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257648, 状态=1, 剩余量=1
2025-08-20 11:02:25.111 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257648, 状态=0, 剩余量=0
2025-08-20 11:02:25.111 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257736, 状态=3, 剩余量=2
2025-08-20 11:02:25.112 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257736, 状态=5, 剩余量=2
2025-08-20 11:02:25.112 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257414, 状态=3, 剩余量=10
2025-08-20 11:02:25.112 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257414, 状态=0, 剩余量=0
2025-08-20 11:02:25.112 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257342, 状态=3, 剩余量=10
2025-08-20 11:02:25.112 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257342, 状态=0, 剩余量=0
2025-08-20 11:02:25.112 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100258082, 状态=3, 剩余量=2
2025-08-20 11:02:25.112 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100258082, 状态=5, 剩余量=2
2025-08-20 11:02:25.113 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100258081, 状态=3, 剩余量=2
2025-08-20 11:02:25.113 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100258083, 状态=3, 剩余量=2
2025-08-20 11:02:25.113 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2504, 订单号=100257349, 状态=3, 剩余量=1
2025-08-20 11:05:28.469 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:43] - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 11:05:28.474 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:60] - 使用本地测试环境配置: localhost:9092
2025-08-20 11:05:28.512 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:89] - 应用本地测试Kafka配置
2025-08-20 11:05:28.566 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:250] - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 11:05:29.453 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 11:05:29.453 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:05:29.455 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:05:29.455 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 11:05:29.455 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 11:05:29.456 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 11:05:29.456 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:05:29.456 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 11:05:29.457 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 11:05:29.457 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 11:05:29.457 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 11:05:29.457 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 11:05:29.457 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 11:05:29.458 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 11:05:29.458 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 11:05:29.458 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 11:05:29.458 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 11:05:29.458 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:05:29.458 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 11:05:29.458 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:05:29.459 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 11:05:29.459 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:05:29.459 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 11:05:29.459 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:05:29.459 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 11:05:29.459 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 11:05:29.459 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:05:29.459 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 11:05:29.460 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:05:29.460 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 11:05:29.460 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 11:05:29.460 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 11:05:29.460 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 11:05:29.460 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 11:05:29.460 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 11:05:29.460 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 11:05:29.461 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 11:05:29.461 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 11:05:29.461 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 11:05:29.461 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:308] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 11:05:29.461 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 11:05:29.461 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 11:05:29.461 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 11:05:29.461 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 11:05:29.462 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 11:05:29.462 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 11:05:29.462 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 11:05:29.462 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 11:05:29.462 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 11:05:29.462 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 11:05:29.463 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:295] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 11:05:29.463 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 11:05:29.463 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 11:05:29.463 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 11:05:29.463 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:282] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
