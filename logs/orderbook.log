2025-08-20 10:33:03.866 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.867 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.868 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:291] - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=3, 剩余量=5, 时间戳=1742173236280
2025-08-20 10:33:03.869 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:358] - [基础层-特别关注] 目标订单10000000未成交在队列: 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:206] - 注册定时器: 当前事件时间=1742173236280, 当前窗口结束=1742173236500, 下次窗口结束=1742173237000
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=10000001, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:361] - [基础层] 单腿订单未成交在队列: 订单号=10000001, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=10000001, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:331] - [基础层] 单腿订单全部成交，移除: 订单号=10000001, 合约=JD2504-C-3200
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=10000005, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:361] - [基础层] 单腿订单未成交在队列: 订单号=10000005, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=10000005, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:331] - [基础层] 单腿订单全部成交，移除: 订单号=10000005, 合约=JD2504-C-3200
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=10000009, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:361] - [基础层] 单腿订单未成交在队列: 订单号=10000009, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=10000009, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:331] - [基础层] 单腿订单全部成交，移除: 订单号=10000009, 合约=JD2504-C-3200
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=10000011, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:361] - [基础层] 单腿订单未成交在队列: 订单号=10000011, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=10000011, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:331] - [基础层] 单腿订单全部成交，移除: 订单号=10000011, 合约=JD2504-C-3200
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=10000017, 合约=CS2505-C-2750, 状态=3, 剩余量=1
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:361] - [基础层] 单腿订单未成交在队列: 订单号=10000017, 合约=CS2505-C-2750, 剩余量=1
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:206] - 注册定时器: 当前事件时间=1742173236600, 当前窗口结束=1742173237000, 下次窗口结束=1742173237500
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=10000017, 合约=CS2505-C-2750, 状态=0, 剩余量=0
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:331] - [基础层] 单腿订单全部成交，移除: 订单号=10000017, 合约=CS2505-C-2750
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=10000019, 合约=CS2505-C-2750, 状态=3, 剩余量=1
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:361] - [基础层] 单腿订单未成交在队列: 订单号=10000019, 合约=CS2505-C-2750, 剩余量=1
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=10000019, 合约=CS2505-C-2750, 状态=0, 剩余量=0
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:331] - [基础层] 单腿订单全部成交，移除: 订单号=10000019, 合约=CS2505-C-2750
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:291] - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=5, 剩余量=5, 时间戳=1742173239280
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:379] - [基础层-特别关注] 目标订单10000000已撤销，移除: 合约=JD2504-C-3200
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=100257097, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:361] - [基础层] 单腿订单未成交在队列: 订单号=100257097, 合约=EB2505, 剩余量=10
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:206] - 注册定时器: 当前事件时间=1742173247000, 当前窗口结束=1742173247500, 下次窗口结束=1742173248000
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=100257007, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:361] - [基础层] 单腿订单未成交在队列: 订单号=100257007, 合约=EB2505, 剩余量=10
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=100257007, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:331] - [基础层] 单腿订单全部成交，移除: 订单号=100257007, 合约=EB2505
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=100257004, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:361] - [基础层] 单腿订单未成交在队列: 订单号=100257004, 合约=EB2505, 剩余量=10
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=100257004, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:331] - [基础层] 单腿订单全部成交，移除: 订单号=100257004, 合约=EB2505
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:313] - [基础层] 处理单腿订单: 订单号=100257098, 合约=EB2505, 状态=3, 剩余量=3
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:361] - [基础层] 单腿订单未成交在队列: 订单号=100257098, 合约=EB2505, 剩余量=3
2025-08-20 10:33:03.876 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.876 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.876 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:33:03.876 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=3, 剩余量=13
2025-08-20 10:33:03.876 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:420] - [虚拟层] 创建新的组合订单状态追踪: 订单号=100257414, 初始量=13
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:461] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=13
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=3, 剩余量=13
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:461] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=13
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:206] - 注册定时器: 当前事件时间=1742173248260, 当前窗口结束=1742173248500, 下次窗口结束=1742173249000
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=12
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:434] - [虚拟层] 组合订单第1轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=1, 完成度={:.1f}%
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=12
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=12
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=12
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=11
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:434] - [虚拟层] 组合订单第2轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=2, 完成度={:.1f}%
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=11
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=11
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=11
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=10
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:434] - [虚拟层] 组合订单第3轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=3, 完成度={:.1f}%
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=10
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=10
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=10
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=9
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:434] - [虚拟层] 组合订单第4轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=4, 完成度={:.1f}%
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=9
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=9
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=9
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=8
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:434] - [虚拟层] 组合订单第5轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=5, 完成度={:.1f}%
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=8
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=8
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=8
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=7
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:434] - [虚拟层] 组合订单第6轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=6, 完成度={:.1f}%
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=7
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=7
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=7
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=6
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:434] - [虚拟层] 组合订单第7轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=7, 完成度={:.1f}%
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=6
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=6
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=6
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=4
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:434] - [虚拟层] 组合订单第8轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=9, 完成度={:.1f}%
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=4
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=2
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:434] - [虚拟层] 组合订单第9轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=11, 完成度={:.1f}%
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=2
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:434] - [虚拟层] 组合订单第10轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=12, 完成度={:.1f}%
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:450] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=0
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=0, 剩余量=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:443] - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=0, 剩余量=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:443] - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=4
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:420] - [虚拟层] 创建新的组合订单状态追踪: 订单号=100257400, 初始量=4
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:461] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=4
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:461] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257401, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:420] - [虚拟层] 创建新的组合订单状态追踪: 订单号=100257401, 初始量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:461] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257401, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:461] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257401, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257401, 两条腿已在虚拟层处理
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:222] - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:434] - [虚拟层] 组合订单第1轮成交: 订单号=100257400, 本轮成交量=2, 累计成交量=2, 完成度={:.1f}%
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:461] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:406] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:461] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:269] - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:33:03.971 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173236500
2025-08-20 10:33:03.971 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.972 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.973 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173237000, 重置定时器标志
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173237000
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173237500, 重置定时器标志
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173237000
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173237500, 重置定时器标志
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173237500
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173238000, 重置定时器标志
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173237500
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.974 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173238000, 重置定时器标志
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173238000
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173238500, 重置定时器标志
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173238000
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173238500, 重置定时器标志
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173238500
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173239000, 重置定时器标志
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173238500
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173239000, 重置定时器标志
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173239000
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.975 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173239500, 重置定时器标志
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173239000
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173239500, 重置定时器标志
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173239500
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173240000, 重置定时器标志
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173239500
2025-08-20 10:33:03.976 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173240000, 重置定时器标志
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173240000
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173240500, 重置定时器标志
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173240000
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.977 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173240500, 重置定时器标志
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173240500
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173241000, 重置定时器标志
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173240500
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173241000, 重置定时器标志
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173241000
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173241500, 重置定时器标志
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173241000
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.978 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173241500, 重置定时器标志
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173241500
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173242000, 重置定时器标志
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173241500
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173242000, 重置定时器标志
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173242000
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173242500, 重置定时器标志
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173242000
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173242500, 重置定时器标志
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173242500
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.979 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173243000, 重置定时器标志
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173242500
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173243000, 重置定时器标志
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173243000
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173243500, 重置定时器标志
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173243000
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173243500, 重置定时器标志
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173243500
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173244000, 重置定时器标志
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173243500
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173244000, 重置定时器标志
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173244000
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173244500, 重置定时器标志
2025-08-20 10:33:03.980 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173244000
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173244500, 重置定时器标志
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173244500
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173245000, 重置定时器标志
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173244500
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173245000, 重置定时器标志
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173245000
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173245500, 重置定时器标志
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173245000
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.981 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173245500, 重置定时器标志
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173245500
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173246000, 重置定时器标志
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173245500
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173246000, 重置定时器标志
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173246000
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173246500, 重置定时器标志
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173246000
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173246500, 重置定时器标志
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173246500
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173247000, 重置定时器标志
2025-08-20 10:33:03.982 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173246500
2025-08-20 10:33:03.983 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.983 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:03.983 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:03.983 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173247000, 重置定时器标志
2025-08-20 10:33:04.077 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173236500, 水位线=-9223372036854775808
2025-08-20 10:33:04.078 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173237000, 当前合约数: 1
2025-08-20 10:33:04.078 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173237000, 水位线=-9223372036854775808
2025-08-20 10:33:04.078 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173237500, 当前合约数: 1
2025-08-20 10:33:04.078 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173237000, 水位线=-9223372036854775808
2025-08-20 10:33:04.078 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173237500, 当前合约数: 2
2025-08-20 10:33:04.078 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173237500, 水位线=-9223372036854775808
2025-08-20 10:33:04.078 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173238000, 当前合约数: 1
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173237500, 水位线=-9223372036854775808
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173238000, 当前合约数: 2
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173238000, 水位线=-9223372036854775808
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173238500, 当前合约数: 1
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173238000, 水位线=-9223372036854775808
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173238500, 当前合约数: 2
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173238500, 水位线=-9223372036854775808
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173239000, 当前合约数: 1
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173238500, 水位线=-9223372036854775808
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173239000, 当前合约数: 2
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173239000, 水位线=-9223372036854775808
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173239500, 当前合约数: 1
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173239000, 水位线=-9223372036854775808
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173239500, 当前合约数: 2
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173239500, 水位线=-9223372036854775808
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173240000, 当前合约数: 1
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173239500, 水位线=-9223372036854775808
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173240000, 当前合约数: 2
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173240000, 水位线=-9223372036854775808
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173240500, 当前合约数: 1
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173240000, 水位线=-9223372036854775808
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173240500, 当前合约数: 2
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173240500, 水位线=-9223372036854775808
2025-08-20 10:33:04.079 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173241000, 当前合约数: 1
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173240500, 水位线=-9223372036854775808
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173241000, 当前合约数: 2
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173241000, 水位线=-9223372036854775808
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173241500, 当前合约数: 1
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173241000, 水位线=-9223372036854775808
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173241500, 当前合约数: 2
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173241500, 水位线=-9223372036854775808
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173242000, 当前合约数: 1
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173241500, 水位线=-9223372036854775808
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173242000, 当前合约数: 2
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173242000, 水位线=-9223372036854775808
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173242500, 当前合约数: 1
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173242000, 水位线=-9223372036854775808
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173242500, 当前合约数: 2
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173242500, 水位线=-9223372036854775808
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173243000, 当前合约数: 1
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173242500, 水位线=-9223372036854775808
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173243000, 当前合约数: 2
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173243000, 水位线=-9223372036854775808
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173243500, 当前合约数: 1
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173243000, 水位线=-9223372036854775808
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173243500, 当前合约数: 2
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173243500, 水位线=-9223372036854775808
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173244000, 当前合约数: 1
2025-08-20 10:33:04.080 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173243500, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173244000, 当前合约数: 2
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173244000, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173244500, 当前合约数: 1
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173244000, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173244500, 当前合约数: 2
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173244500, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173245000, 当前合约数: 1
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173244500, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173245000, 当前合约数: 2
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173245000, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173245500, 当前合约数: 1
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173245000, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173245500, 当前合约数: 2
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173245500, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173246000, 当前合约数: 1
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173245500, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173246000, 当前合约数: 2
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173246000, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173246500, 当前合约数: 1
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173246000, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173246500, 当前合约数: 2
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173246500, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173247000, 当前合约数: 1
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173246500, 水位线=-9223372036854775808
2025-08-20 10:33:04.081 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173247000, 当前合约数: 2
2025-08-20 10:33:04.082 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173237000, 触发时间=1742173237250, 水位线=1742173246899
2025-08-20 10:33:04.082 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173237000, 合约数=1
2025-08-20 10:33:04.082 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173237500, 触发时间=1742173237750, 水位线=1742173246899
2025-08-20 10:33:04.082 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173237500, 合约数=2
2025-08-20 10:33:04.082 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173238000, 触发时间=1742173238250, 水位线=1742173246899
2025-08-20 10:33:04.082 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173238000, 合约数=2
2025-08-20 10:33:04.082 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173238500, 触发时间=1742173238750, 水位线=1742173246899
2025-08-20 10:33:04.082 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173238500, 合约数=2
2025-08-20 10:33:04.082 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173239000, 触发时间=1742173239250, 水位线=1742173246899
2025-08-20 10:33:04.082 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173239000, 合约数=2
2025-08-20 10:33:04.082 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173239500, 触发时间=1742173239750, 水位线=1742173246899
2025-08-20 10:33:04.082 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173239500, 合约数=2
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173240000, 触发时间=1742173240250, 水位线=1742173246899
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173240000, 合约数=2
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173240500, 触发时间=1742173240750, 水位线=1742173246899
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173240500, 合约数=2
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173241000, 触发时间=1742173241250, 水位线=1742173246899
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173241000, 合约数=2
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173241500, 触发时间=1742173241750, 水位线=1742173246899
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173241500, 合约数=2
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173242000, 触发时间=1742173242250, 水位线=1742173246899
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173242000, 合约数=2
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173242500, 触发时间=1742173242750, 水位线=1742173246899
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173242500, 合约数=2
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173243000, 触发时间=1742173243250, 水位线=1742173246899
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173243000, 合约数=2
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173243500, 触发时间=1742173243750, 水位线=1742173246899
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173243500, 合约数=2
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173244000, 触发时间=1742173244250, 水位线=1742173246899
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173244000, 合约数=2
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173244500, 触发时间=1742173244750, 水位线=1742173246899
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173244500, 合约数=2
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173245000, 触发时间=1742173245250, 水位线=1742173246899
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173245000, 合约数=2
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173245500, 触发时间=1742173245750, 水位线=1742173246899
2025-08-20 10:33:04.083 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173245500, 合约数=2
2025-08-20 10:33:04.084 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173246000, 触发时间=1742173246250, 水位线=1742173246899
2025-08-20 10:33:04.084 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173246000, 合约数=2
2025-08-20 10:33:04.084 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173246500, 触发时间=1742173246750, 水位线=1742173246899
2025-08-20 10:33:04.084 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173246500, 合约数=2
2025-08-20 10:33:05.209 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173247000
2025-08-20 10:33:05.209 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.210 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.210 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.210 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173247500, 重置定时器标志
2025-08-20 10:33:05.210 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173247000
2025-08-20 10:33:05.210 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.210 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.210 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.210 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173247500, 重置定时器标志
2025-08-20 10:33:05.211 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 EB2505 生成分层订单簿快照，时间戳=1742173247500
2025-08-20 10:33:05.211 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 EB2505 基础层处理: 2个单腿订单, 买盘2档, 卖盘0档
2025-08-20 10:33:05.211 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 EB2505 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.211 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:582] - [分层订单簿] 合约 EB2505 输出快照: 基础层(2/0档) + 虚拟层(0/0档) = 最终(2/0档)
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 EB2505 注册下次定时器: 1742173248000, 重置定时器标志
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173247500
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173248000, 重置定时器标志
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173247500
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173248000, 重置定时器标志
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173248000
2025-08-20 10:33:05.212 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173248500, 重置定时器标志
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173248000
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173248500, 重置定时器标志
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 EB2505 生成分层订单簿快照，时间戳=1742173248000
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 EB2505 基础层处理: 2个单腿订单, 买盘2档, 卖盘0档
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 EB2505 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:582] - [分层订单簿] 合约 EB2505 输出快照: 基础层(2/0档) + 虚拟层(0/0档) = 最终(2/0档)
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 EB2505 注册下次定时器: 1742173248500, 重置定时器标志
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173248500
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173249000, 重置定时器标志
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 EB2505 生成分层订单簿快照，时间戳=1742173248500
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 EB2505 基础层处理: 2个单腿订单, 买盘2档, 卖盘0档
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 EB2505 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:582] - [分层订单簿] 合约 EB2505 输出快照: 基础层(2/0档) + 虚拟层(0/0档) = 最终(2/0档)
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 EB2505 注册下次定时器: 1742173249000, 重置定时器标志
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173248500
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173249000, 重置定时器标志
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 EB2504 生成分层订单簿快照，时间戳=1742173248500
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 EB2504 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.215 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173247000, 水位线=1742173246899
2025-08-20 10:33:05.215 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173247500, 当前合约数: 1
2025-08-20 10:33:05.215 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173247000, 水位线=1742173246899
2025-08-20 10:33:05.215 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173247500, 当前合约数: 2
2025-08-20 10:33:05.215 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:636] - [虚拟层-简化挂单] 订单号=100257400, 腿=1, 方向=B, 数量=2
2025-08-20 10:33:05.215 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:636] - [虚拟层-简化挂单] 订单号=100257400, 腿=2, 方向=S, 数量=2
2025-08-20 10:33:05.216 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:636] - [虚拟层-简化挂单] 订单号=100257401, 腿=1, 方向=B, 数量=2
2025-08-20 10:33:05.216 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=EB2505, 事件时间=1742173247500, 水位线=1742173246899
2025-08-20 10:33:05.216 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:636] - [虚拟层-简化挂单] 订单号=100257401, 腿=2, 方向=S, 数量=2
2025-08-20 10:33:05.216 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173248000, 当前合约数: 1
2025-08-20 10:33:05.216 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 EB2504 虚拟层处理: 4个组合订单腿, 4个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.216 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 EB2504 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.216 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 EB2504 注册下次定时器: 1742173249000, 重置定时器标志
2025-08-20 10:33:05.216 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173249000
2025-08-20 10:33:05.216 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.216 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.216 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.216 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173249500, 重置定时器标志
2025-08-20 10:33:05.216 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 EB2504 生成分层订单簿快照，时间戳=1742173249000
2025-08-20 10:33:05.216 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 EB2504 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.217 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:636] - [虚拟层-简化挂单] 订单号=100257400, 腿=1, 方向=B, 数量=2
2025-08-20 10:33:05.217 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:636] - [虚拟层-简化挂单] 订单号=100257400, 腿=2, 方向=S, 数量=2
2025-08-20 10:33:05.217 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:636] - [虚拟层-简化挂单] 订单号=100257401, 腿=1, 方向=B, 数量=2
2025-08-20 10:33:05.217 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:636] - [虚拟层-简化挂单] 订单号=100257401, 腿=2, 方向=S, 数量=2
2025-08-20 10:33:05.217 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 EB2504 虚拟层处理: 4个组合订单腿, 4个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.217 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 EB2504 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.217 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 EB2504 注册下次定时器: 1742173249500, 重置定时器标志
2025-08-20 10:33:05.217 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173249000
2025-08-20 10:33:05.217 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.217 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.217 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173249500, 重置定时器标志
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 EB2505 生成分层订单簿快照，时间戳=1742173249000
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 EB2505 基础层处理: 2个单腿订单, 买盘2档, 卖盘0档
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 EB2505 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:582] - [分层订单簿] 合约 EB2505 输出快照: 基础层(2/0档) + 虚拟层(0/0档) = 最终(2/0档)
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 EB2505 注册下次定时器: 1742173249500, 重置定时器标志
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173249500
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 CS2505-C-2750 注册下次定时器: 1742173250000, 重置定时器标志
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 EB2505 生成分层订单簿快照，时间戳=1742173249500
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 EB2505 基础层处理: 2个单腿订单, 买盘2档, 卖盘0档
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 EB2505 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:582] - [分层订单簿] 合约 EB2505 输出快照: 基础层(2/0档) + 虚拟层(0/0档) = 最终(2/0档)
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 EB2505 注册下次定时器: 1742173250000, 重置定时器标志
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173249500
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 JD2504-C-3200 注册下次定时器: 1742173250000, 重置定时器标志
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:490] - 合约 EB2504 生成分层订单簿快照，时间戳=1742173249500
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:527] - [基础层] 合约 EB2504 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:636] - [虚拟层-简化挂单] 订单号=100257400, 腿=1, 方向=B, 数量=2
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:636] - [虚拟层-简化挂单] 订单号=100257400, 腿=2, 方向=S, 数量=2
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:636] - [虚拟层-简化挂单] 订单号=100257401, 腿=1, 方向=B, 数量=2
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:636] - [虚拟层-简化挂单] 订单号=100257401, 腿=2, 方向=S, 数量=2
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:554] - [虚拟层] 合约 EB2504 虚拟层处理: 4个组合订单腿, 4个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:33:05.220 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:590] - [分层订单簿] 合约 EB2504 订单簿为空，但仍输出空快照
2025-08-20 10:33:05.220 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:606] - 合约 EB2504 注册下次定时器: 1742173250000, 重置定时器标志
2025-08-20 10:33:05.220 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:415] - 实时快照: 合约:EB2505 买一:8034.00(10)
2025-08-20 10:33:05.220 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173247500, 水位线=1742173246899
2025-08-20 10:33:05.220 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173248000, 当前合约数: 2
2025-08-20 10:33:05.220 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173247500, 水位线=1742173246899
2025-08-20 10:33:05.220 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173248000, 当前合约数: 3
2025-08-20 10:33:05.221 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173248000, 水位线=1742173246899
2025-08-20 10:33:05.221 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173248500, 当前合约数: 1
2025-08-20 10:33:05.221 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173248000, 水位线=1742173246899
2025-08-20 10:33:05.221 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173248500, 当前合约数: 2
2025-08-20 10:33:05.221 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=EB2505, 事件时间=1742173248000, 水位线=1742173246899
2025-08-20 10:33:05.221 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173248500, 当前合约数: 3
2025-08-20 10:33:05.221 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:415] - 实时快照: 合约:EB2505 买一:8034.00(10)
2025-08-20 10:33:05.221 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173248500, 水位线=1742173246899
2025-08-20 10:33:05.221 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173249000, 当前合约数: 1
2025-08-20 10:33:05.221 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=EB2505, 事件时间=1742173248500, 水位线=1742173246899
2025-08-20 10:33:05.221 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173249000, 当前合约数: 2
2025-08-20 10:33:05.221 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:415] - 实时快照: 合约:EB2505 买一:8034.00(10)
2025-08-20 10:33:05.222 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173248500, 水位线=1742173246899
2025-08-20 10:33:05.222 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173249000, 当前合约数: 3
2025-08-20 10:33:05.318 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=EB2504, 事件时间=1742173248500, 水位线=1742173246899
2025-08-20 10:33:05.319 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173249000, 当前合约数: 4
2025-08-20 10:33:05.319 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173249000, 水位线=1742173246899
2025-08-20 10:33:05.319 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173249500, 当前合约数: 1
2025-08-20 10:33:05.319 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=EB2504, 事件时间=1742173249000, 水位线=1742173246899
2025-08-20 10:33:05.319 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173249500, 当前合约数: 2
2025-08-20 10:33:05.319 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173249000, 水位线=1742173246899
2025-08-20 10:33:05.319 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173249500, 当前合约数: 3
2025-08-20 10:33:05.319 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=EB2505, 事件时间=1742173249000, 水位线=1742173246899
2025-08-20 10:33:05.319 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173249500, 当前合约数: 4
2025-08-20 10:33:05.320 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:415] - 实时快照: 合约:EB2505 买一:8034.00(10)
2025-08-20 10:33:05.320 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173249500, 水位线=1742173246899
2025-08-20 10:33:05.320 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173250000, 当前合约数: 1
2025-08-20 10:33:05.320 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=EB2505, 事件时间=1742173249500, 水位线=1742173246899
2025-08-20 10:33:05.320 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173250000, 当前合约数: 2
2025-08-20 10:33:05.320 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:415] - 实时快照: 合约:EB2505 买一:8034.00(10)
2025-08-20 10:33:05.320 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173249500, 水位线=1742173246899
2025-08-20 10:33:05.320 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173250000, 当前合约数: 3
2025-08-20 10:33:05.321 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:378] - 接收快照: 合约=EB2504, 事件时间=1742173249500, 水位线=1742173246899
2025-08-20 10:33:05.321 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:407] - 缓存快照到窗口: 1742173250000, 当前合约数: 4
2025-08-20 10:33:05.321 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173247000, 触发时间=1742173247250, 水位线=1742173249959
2025-08-20 10:33:05.321 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173247000, 合约数=2
2025-08-20 10:33:05.321 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173247500, 触发时间=1742173247750, 水位线=1742173249959
2025-08-20 10:33:05.321 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173247500, 合约数=2
2025-08-20 10:33:05.321 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173248000, 触发时间=1742173248250, 水位线=1742173249959
2025-08-20 10:33:05.321 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173248000, 合约数=3
2025-08-20 10:33:05.325 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173248500, 触发时间=1742173248750, 水位线=1742173249959
2025-08-20 10:33:05.325 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173248500, 合约数=3
2025-08-20 10:33:05.326 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173249000, 触发时间=1742173249250, 水位线=1742173249959
2025-08-20 10:33:05.326 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173249000, 合约数=4
2025-08-20 10:33:05.326 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:424] - 定时器触发: 窗口结束时间=1742173249500, 触发时间=1742173249750, 水位线=1742173249959
2025-08-20 10:33:05.326 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:430] - 输出全局快照: 窗口时间=1742173249500, 合约数=4
2025-08-20 10:56:05.410 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.411 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.411 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:292] - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=3, 剩余量=5, 时间戳=1742173236280
2025-08-20 10:56:05.412 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:365] - [基础层-特别关注] 目标订单10000000未成交在队列: 合约=JD2504-C-3200, 剩余量=5, 验证成功
2025-08-20 10:56:05.413 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236280, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.413 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.413 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.413 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000001, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.413 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000001, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000001, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000001, 合约=JD2504-C-3200
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000005, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000005, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000005, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000005, 合约=JD2504-C-3200
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000009, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000009, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000009, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000009, 合约=JD2504-C-3200
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000011, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000011, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000011, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000011, 合约=JD2504-C-3200
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000017, 合约=CS2505-C-2750, 状态=3, 剩余量=1
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000017, 合约=CS2505-C-2750, 剩余量=1
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000017, 合约=CS2505-C-2750, 状态=0, 剩余量=0
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000017, 合约=CS2505-C-2750
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000019, 合约=CS2505-C-2750, 状态=3, 剩余量=1
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000019, 合约=CS2505-C-2750, 剩余量=1
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000019, 合约=CS2505-C-2750, 状态=0, 剩余量=0
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000019, 合约=CS2505-C-2750
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:292] - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=5, 剩余量=5, 时间戳=1742173239280
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:395] - [基础层-特别关注] 目标订单10000000已撤销，移除: 合约=JD2504-C-3200, 验证成功
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173239280, 注册窗口范围=1742173239500 到 1742173245000 (共12个窗口)
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257097, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257097, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257007, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257007, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257007, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257007, 合约=EB2505
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257004, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257004, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257004, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257004, 合约=EB2505
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257098, 合约=EB2505, 状态=3, 剩余量=3
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257098, 合约=EB2505, 剩余量=3
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:292] - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=3, 剩余量=5, 时间戳=1742173236280
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:365] - [基础层-特别关注] 目标订单10000000未成交在队列: 合约=JD2504-C-3200, 剩余量=5, 验证成功
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236280, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000001, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000001, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000001, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000001, 合约=JD2504-C-3200
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000005, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000005, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000005, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000005, 合约=JD2504-C-3200
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000009, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000009, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000009, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000009, 合约=JD2504-C-3200
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000011, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000011, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000011, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000011, 合约=JD2504-C-3200
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000017, 合约=CS2505-C-2750, 状态=3, 剩余量=1
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000017, 合约=CS2505-C-2750, 剩余量=1
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000017, 合约=CS2505-C-2750, 状态=0, 剩余量=0
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000017, 合约=CS2505-C-2750
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000019, 合约=CS2505-C-2750, 状态=3, 剩余量=1
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000019, 合约=CS2505-C-2750, 剩余量=1
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000019, 合约=CS2505-C-2750, 状态=0, 剩余量=0
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000019, 合约=CS2505-C-2750
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:292] - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=5, 剩余量=5, 时间戳=1742173239280
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:395] - [基础层-特别关注] 目标订单10000000已撤销，移除: 合约=JD2504-C-3200, 验证成功
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173239280, 注册窗口范围=1742173239500 到 1742173245000 (共12个窗口)
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257097, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257097, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257007, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257007, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257007, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257007, 合约=EB2505
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257004, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257004, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257004, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257004, 合约=EB2505
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257098, 合约=EB2505, 状态=3, 剩余量=3
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257098, 合约=EB2505, 剩余量=3
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000021, 合约=SH2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000021, 合约=SH2505, 剩余量=1
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000021, 合约=SH2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000021, 合约=SH2505
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000023, 合约=SH2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000023, 合约=SH2505, 剩余量=1
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000023, 合约=SH2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000023, 合约=SH2505
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257000, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257000, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257001, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257001, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257002, 合约=EB2504, 状态=3, 剩余量=5
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257002, 合约=EB2504, 剩余量=5
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257003, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257003, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257009, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257009, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257005, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257005, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257006, 合约=EB2505, 状态=3, 剩余量=5
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257006, 合约=EB2505, 剩余量=5
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257008, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257008, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100256966, 合约=EB2505, 状态=3, 剩余量=9
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100256966, 合约=EB2505, 剩余量=9
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257085, 合约=EB2505, 状态=3, 剩余量=8
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257085, 合约=EB2505, 剩余量=8
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257085, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257085, 合约=EB2505
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100256966, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:344] - [基础层] 单腿订单部分成交，更新: 订单号=100256966, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248179, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100256966, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:344] - [基础层] 单腿订单部分成交，更新: 订单号=100256966, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248179, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257259, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257259, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248179, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257259, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257259, 合约=EB2505
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248179, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100256966, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100256966, 合约=EB2505
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100254367, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100254367, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248326, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100254367, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100254367, 合约=EB2505
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248326, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257479, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257479, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248326, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257479, 合约=EB2504, 状态=0, 剩余量=0
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257479, 合约=EB2504
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248326, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100256192, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100256192, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248344, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100256192, 合约=EB2504, 状态=0, 剩余量=0
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100256192, 合约=EB2504
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248344, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257507, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257507, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248344, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257507, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257507, 合约=EB2505
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248344, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257466, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257466, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248419, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257466, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257466, 合约=EB2505
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248419, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:292] - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=3, 剩余量=5, 时间戳=1742173236280
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:365] - [基础层-特别关注] 目标订单10000000未成交在队列: 合约=JD2504-C-3200, 剩余量=5, 验证成功
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236280, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000001, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000001, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000001, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000001, 合约=JD2504-C-3200
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000005, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000005, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000005, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000005, 合约=JD2504-C-3200
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000009, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000009, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000009, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000009, 合约=JD2504-C-3200
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000011, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000011, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000011, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000011, 合约=JD2504-C-3200
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000017, 合约=CS2505-C-2750, 状态=3, 剩余量=1
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000017, 合约=CS2505-C-2750, 剩余量=1
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000017, 合约=CS2505-C-2750, 状态=0, 剩余量=0
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000017, 合约=CS2505-C-2750
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000019, 合约=CS2505-C-2750, 状态=3, 剩余量=1
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000019, 合约=CS2505-C-2750, 剩余量=1
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000019, 合约=CS2505-C-2750, 状态=0, 剩余量=0
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000019, 合约=CS2505-C-2750
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:292] - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=5, 剩余量=5, 时间戳=1742173239280
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:395] - [基础层-特别关注] 目标订单10000000已撤销，移除: 合约=JD2504-C-3200, 验证成功
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173239280, 注册窗口范围=1742173239500 到 1742173245000 (共12个窗口)
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257097, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257097, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257007, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257007, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257007, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257007, 合约=EB2505
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257004, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257004, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257004, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257004, 合约=EB2505
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257098, 合约=EB2505, 状态=3, 剩余量=3
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257098, 合约=EB2505, 剩余量=3
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000021, 合约=SH2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000021, 合约=SH2505, 剩余量=1
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000021, 合约=SH2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000021, 合约=SH2505
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000023, 合约=SH2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000023, 合约=SH2505, 剩余量=1
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000023, 合约=SH2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000023, 合约=SH2505
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257000, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257000, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257001, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257001, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257002, 合约=EB2504, 状态=3, 剩余量=5
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257002, 合约=EB2504, 剩余量=5
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257003, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257003, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257009, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257009, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257005, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257005, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257006, 合约=EB2505, 状态=3, 剩余量=5
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257006, 合约=EB2505, 剩余量=5
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257008, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257008, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100256966, 合约=EB2505, 状态=3, 剩余量=9
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100256966, 合约=EB2505, 剩余量=9
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257085, 合约=EB2505, 状态=3, 剩余量=8
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257085, 合约=EB2505, 剩余量=8
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257085, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257085, 合约=EB2505
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100256966, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:344] - [基础层] 单腿订单部分成交，更新: 订单号=100256966, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248179, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100256966, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:344] - [基础层] 单腿订单部分成交，更新: 订单号=100256966, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248179, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257259, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257259, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248179, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257259, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257259, 合约=EB2505
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248179, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100256966, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100256966, 合约=EB2505
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100254367, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100254367, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248326, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100254367, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100254367, 合约=EB2505
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248326, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257479, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257479, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248326, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257479, 合约=EB2504, 状态=0, 剩余量=0
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257479, 合约=EB2504
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248326, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100256192, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100256192, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248344, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100256192, 合约=EB2504, 状态=0, 剩余量=0
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100256192, 合约=EB2504
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248344, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257507, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257507, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248344, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257507, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257507, 合约=EB2505
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248344, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257466, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257466, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248419, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257466, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257466, 合约=EB2505
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248419, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:292] - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=3, 剩余量=5, 时间戳=1742173236280
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:365] - [基础层-特别关注] 目标订单10000000未成交在队列: 合约=JD2504-C-3200, 剩余量=5, 验证成功
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236280, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000001, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000001, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000001, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000001, 合约=JD2504-C-3200
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000005, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000005, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000005, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000005, 合约=JD2504-C-3200
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000009, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000009, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000009, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000009, 合约=JD2504-C-3200
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000011, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000011, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000011, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000011, 合约=JD2504-C-3200
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000017, 合约=CS2505-C-2750, 状态=3, 剩余量=1
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000017, 合约=CS2505-C-2750, 剩余量=1
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000017, 合约=CS2505-C-2750, 状态=0, 剩余量=0
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000017, 合约=CS2505-C-2750
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000019, 合约=CS2505-C-2750, 状态=3, 剩余量=1
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000019, 合约=CS2505-C-2750, 剩余量=1
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000019, 合约=CS2505-C-2750, 状态=0, 剩余量=0
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000019, 合约=CS2505-C-2750
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:292] - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=5, 剩余量=5, 时间戳=1742173239280
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:395] - [基础层-特别关注] 目标订单10000000已撤销，移除: 合约=JD2504-C-3200, 验证成功
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173239280, 注册窗口范围=1742173239500 到 1742173245000 (共12个窗口)
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257097, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257097, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257007, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257007, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257007, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257007, 合约=EB2505
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257004, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257004, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257004, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257004, 合约=EB2505
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257098, 合约=EB2505, 状态=3, 剩余量=3
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257098, 合约=EB2505, 剩余量=3
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000021, 合约=SH2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000021, 合约=SH2505, 剩余量=1
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000021, 合约=SH2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000021, 合约=SH2505
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000023, 合约=SH2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000023, 合约=SH2505, 剩余量=1
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000023, 合约=SH2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000023, 合约=SH2505
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257000, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257000, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257001, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257001, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257002, 合约=EB2504, 状态=3, 剩余量=5
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257002, 合约=EB2504, 剩余量=5
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257003, 合约=EB2504, 状态=3, 剩余量=1
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257003, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257009, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257009, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257005, 合约=EB2505, 状态=3, 剩余量=1
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257005, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248000, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:292] - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=3, 剩余量=5, 时间戳=1742173236280
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:365] - [基础层-特别关注] 目标订单10000000未成交在队列: 合约=JD2504-C-3200, 剩余量=5, 验证成功
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236280, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000001, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000001, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000001, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000001, 合约=JD2504-C-3200
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000005, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000005, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000005, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000005, 合约=JD2504-C-3200
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000009, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000009, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000009, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000009, 合约=JD2504-C-3200
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000011, 合约=JD2504-C-3200, 状态=3, 剩余量=5
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000011, 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000011, 合约=JD2504-C-3200, 状态=0, 剩余量=0
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000011, 合约=JD2504-C-3200
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236480, 注册窗口范围=1742173236500 到 1742173242000 (共12个窗口)
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000017, 合约=CS2505-C-2750, 状态=3, 剩余量=1
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000017, 合约=CS2505-C-2750, 剩余量=1
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000017, 合约=CS2505-C-2750, 状态=0, 剩余量=0
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000017, 合约=CS2505-C-2750
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000019, 合约=CS2505-C-2750, 状态=3, 剩余量=1
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=10000019, 合约=CS2505-C-2750, 剩余量=1
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=10000019, 合约=CS2505-C-2750, 状态=0, 剩余量=0
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=10000019, 合约=CS2505-C-2750
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173236600, 注册窗口范围=1742173237000 到 1742173242500 (共12个窗口)
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:292] - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=5, 剩余量=5, 时间戳=1742173239280
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:395] - [基础层-特别关注] 目标订单10000000已撤销，移除: 合约=JD2504-C-3200, 验证成功
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173239280, 注册窗口范围=1742173239500 到 1742173245000 (共12个窗口)
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257097, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257097, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257007, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257007, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257007, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257007, 合约=EB2505
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257004, 合约=EB2505, 状态=3, 剩余量=10
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257004, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257004, 合约=EB2505, 状态=0, 剩余量=0
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:332] - [基础层] 单腿订单全部成交，移除: 订单号=100257004, 合约=EB2505
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:179] - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:314] - [基础层] 处理单腿订单: 订单号=100257098, 合约=EB2505, 状态=3, 剩余量=3
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:372] - [基础层] 单腿订单未成交在队列: 订单号=100257098, 合约=EB2505, 剩余量=3
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173247000, 注册窗口范围=1742173247500 到 1742173253000 (共12个窗口)
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=3, 剩余量=13
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:440] - [虚拟层] 创建新的组合订单状态追踪: 订单号=100257414, 初始量=13
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=13
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=3, 剩余量=13
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=13
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=12
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第1轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=1, 完成度={:.1f}%
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=12
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=12
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=12
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=11
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第2轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=2, 完成度={:.1f}%
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=11
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=11
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=11
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=10
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第3轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=3, 完成度={:.1f}%
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=10
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=10
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=9
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第4轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=4, 完成度={:.1f}%
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=9
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=9
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=9
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=8
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第5轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=5, 完成度={:.1f}%
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=8
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=8
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=8
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=7
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第6轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=6, 完成度={:.1f}%
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=7
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=7
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=7
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=6
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第7轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=7, 完成度={:.1f}%
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=6
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=6
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=6
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第8轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=9, 完成度={:.1f}%
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=2
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第9轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=11, 完成度={:.1f}%
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=2
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第10轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=12, 完成度={:.1f}%
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=0
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=0, 剩余量=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:463] - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=0, 剩余量=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:463] - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:440] - [虚拟层] 创建新的组合订单状态追踪: 订单号=100257400, 初始量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=4
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248760, 注册窗口范围=1742173249000 到 1742173254500 (共12个窗口)
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257401, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:440] - [虚拟层] 创建新的组合订单状态追踪: 订单号=100257401, 初始量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257401, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257401, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257401, 两条腿已在虚拟层处理
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173249460, 注册窗口范围=1742173249500 到 1742173255000 (共12个窗口)
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第1轮成交: 订单号=100257400, 本轮成交量=2, 累计成交量=2, 完成度={:.1f}%
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173250160, 注册窗口范围=1742173250500 到 1742173256000 (共12个窗口)
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=3, 剩余量=13
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=13
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=3, 剩余量=13
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=13
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=12
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第11轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=13, 完成度={:.1f}%
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=12
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=12
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=12
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=11
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第12轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=14, 完成度={:.1f}%
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=11
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=11
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=11
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=10
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第13轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=15, 完成度={:.1f}%
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=10
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=10
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=9
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第14轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=16, 完成度={:.1f}%
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=9
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=9
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=9
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=8
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第15轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=17, 完成度={:.1f}%
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=8
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=8
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=8
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=7
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第16轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=18, 完成度={:.1f}%
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=7
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=7
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=7
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=6
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第17轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=19, 完成度={:.1f}%
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=6
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=6
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=6
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=4
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第18轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=21, 完成度={:.1f}%
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=4
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=2
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第19轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=23, 完成度={:.1f}%
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=2
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第20轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=24, 完成度={:.1f}%
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=0
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=0, 剩余量=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:463] - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=0, 剩余量=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:463] - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=4
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=4
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248760, 注册窗口范围=1742173249000 到 1742173254500 (共12个窗口)
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257401, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257401, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257401, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257401, 两条腿已在虚拟层处理
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173249460, 注册窗口范围=1742173249500 到 1742173255000 (共12个窗口)
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第2轮成交: 订单号=100257400, 本轮成交量=2, 累计成交量=4, 完成度={:.1f}%
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173250160, 注册窗口范围=1742173250500 到 1742173256000 (共12个窗口)
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=3, 剩余量=13
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=13
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=3, 剩余量=13
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=13
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=12
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第21轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=25, 完成度={:.1f}%
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=12
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=12
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=12
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=11
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第22轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=26, 完成度={:.1f}%
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=11
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=11
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=11
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=10
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第23轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=27, 完成度={:.1f}%
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=10
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=10
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=9
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第24轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=28, 完成度={:.1f}%
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=9
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=9
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=9
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=8
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第25轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=29, 完成度={:.1f}%
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=8
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=8
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=8
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=7
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第26轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=30, 完成度={:.1f}%
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=7
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=7
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=7
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=6
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第27轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=31, 完成度={:.1f}%
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=6
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=6
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=6
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第28轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=33, 完成度={:.1f}%
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=2
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第29轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=35, 完成度={:.1f}%
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=2
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第30轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=36, 完成度={:.1f}%
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=0
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=0, 剩余量=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:463] - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=0, 剩余量=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:463] - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248760, 注册窗口范围=1742173249000 到 1742173254500 (共12个窗口)
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257401, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257401, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257401, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257401, 两条腿已在虚拟层处理
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173249460, 注册窗口范围=1742173249500 到 1742173255000 (共12个窗口)
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第3轮成交: 订单号=100257400, 本轮成交量=2, 累计成交量=6, 完成度={:.1f}%
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173250160, 注册窗口范围=1742173250500 到 1742173256000 (共12个窗口)
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=3, 剩余量=13
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=13
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=3, 剩余量=13
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=13
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=12
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第31轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=37, 完成度={:.1f}%
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=12
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=12
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=12
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=11
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第32轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=38, 完成度={:.1f}%
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=11
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=11
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=11
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=10
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第33轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=39, 完成度={:.1f}%
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=10
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=10
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=9
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第34轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=40, 完成度={:.1f}%
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=9
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=9
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=9
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=8
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第35轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=41, 完成度={:.1f}%
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=8
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=8
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=8
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=7
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第36轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=42, 完成度={:.1f}%
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=7
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=7
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=7
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=6
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第37轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=43, 完成度={:.1f}%
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=6
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=6
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=6
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第38轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=45, 完成度={:.1f}%
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=2
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第39轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=47, 完成度={:.1f}%
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=2
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第40轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=48, 完成度={:.1f}%
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=0
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=0, 剩余量=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:463] - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=0, 剩余量=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:463] - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248760, 注册窗口范围=1742173249000 到 1742173254500 (共12个窗口)
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257401, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257401, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257401, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257401, 两条腿已在虚拟层处理
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173249460, 注册窗口范围=1742173249500 到 1742173255000 (共12个窗口)
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第4轮成交: 订单号=100257400, 本轮成交量=2, 累计成交量=8, 完成度={:.1f}%
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173250160, 注册窗口范围=1742173250500 到 1742173256000 (共12个窗口)
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=3, 剩余量=13
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=13
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=3, 剩余量=13
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=13
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=12
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第41轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=49, 完成度={:.1f}%
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=12
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=12
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=12
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=11
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第42轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=50, 完成度={:.1f}%
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=11
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=11
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=11
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=10
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第43轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=51, 完成度={:.1f}%
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=10
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=10
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=9
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第44轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=52, 完成度={:.1f}%
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=9
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=9
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=9
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=8
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第45轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=53, 完成度={:.1f}%
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=8
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=8
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=8
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=7
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第46轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=54, 完成度={:.1f}%
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=7
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=7
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=7
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=6
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第47轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=55, 完成度={:.1f}%
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=6
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=6
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=6
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=4
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第48轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=57, 完成度={:.1f}%
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=4
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第49轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=59, 完成度={:.1f}%
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第50轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=60, 完成度={:.1f}%
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:470] - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=0
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=0, 剩余量=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:463] - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=0, 剩余量=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:463] - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248260, 注册窗口范围=1742173248500 到 1742173254000 (共12个窗口)
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=4
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=4
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173248760, 注册窗口范围=1742173249000 到 1742173254500 (共12个窗口)
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257401, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257401, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257401, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257401, 两条腿已在虚拟层处理
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173249460, 注册窗口范围=1742173249500 到 1742173255000 (共12个窗口)
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:172] - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:184] - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:223] - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:454] - [虚拟层] 组合订单第5轮成交: 订单号=100257400, 本轮成交量=2, 累计成交量=10, 完成度={:.1f}%
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:426] - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.453 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:481] - [虚拟层] 组合订单腿未成交，保持虚拟挂单: 订单号=100257400, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.453 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:270] - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.453 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:210] - 注册定时器: 当前事件时间=1742173250160, 注册窗口范围=1742173250500 到 1742173256000 (共12个窗口)
2025-08-20 10:56:05.514 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173236500
2025-08-20 10:56:05.514 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.514 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.515 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173237000
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173237000
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173237500
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173237000
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173237500
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173237500
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173238000
2025-08-20 10:56:05.516 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173237500
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173238000
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173238000
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173238500
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173238000
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173238500
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173238500
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173239000
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173238500
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173239000
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173239000
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173239500
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173239000
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.517 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173239500
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173239500
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173240000
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173239500
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173240000
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173240000
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173240500
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173240000
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173240500
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173240500
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173241000
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173240500
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173241000
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173241000
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173241500
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173241000
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173241500
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173241500
2025-08-20 10:56:05.518 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173242000
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173241500
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173242000
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173242000
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173242500
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173242000
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173242500
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173242500
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173243000
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173242500
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173243000
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173243000
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173243500
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173243000
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173243500
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173243500
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.519 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173244000
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173243500
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173244000
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173244000
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173244500
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173244000
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173244500
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173244500
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173245000
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173244500
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173245000
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173245000
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173245500
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173245000
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173245500
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173245500
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173246000
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173245500
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.520 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173246000
2025-08-20 10:56:05.521 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173246000
2025-08-20 10:56:05.521 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.521 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.521 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.521 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173246500
2025-08-20 10:56:05.521 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173246000
2025-08-20 10:56:05.521 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.521 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:05.521 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:05.521 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173246500
2025-08-20 10:56:05.617 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173236500, 水位线=-9223372036854775808
2025-08-20 10:56:05.618 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173237000, 当前合约数: 1
2025-08-20 10:56:05.618 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173237000, 水位线=-9223372036854775808
2025-08-20 10:56:05.618 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173237500, 当前合约数: 1
2025-08-20 10:56:05.618 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173237000, 水位线=-9223372036854775808
2025-08-20 10:56:05.618 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173237500, 当前合约数: 2
2025-08-20 10:56:05.618 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173237500, 水位线=-9223372036854775808
2025-08-20 10:56:05.618 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173238000, 当前合约数: 1
2025-08-20 10:56:05.618 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173237500, 水位线=-9223372036854775808
2025-08-20 10:56:05.618 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173238000, 当前合约数: 2
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173238000, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173238500, 当前合约数: 1
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173238000, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173238500, 当前合约数: 2
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173238500, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173239000, 当前合约数: 1
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173238500, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173239000, 当前合约数: 2
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173239000, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173239500, 当前合约数: 1
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173239000, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173239500, 当前合约数: 2
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173239500, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173240000, 当前合约数: 1
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173239500, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173240000, 当前合约数: 2
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173240000, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173240500, 当前合约数: 1
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173240000, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173240500, 当前合约数: 2
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173240500, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173241000, 当前合约数: 1
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173240500, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173241000, 当前合约数: 2
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173241000, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173241500, 当前合约数: 1
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173241000, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173241500, 当前合约数: 2
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173241500, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173242000, 当前合约数: 1
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173241500, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173242000, 当前合约数: 2
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173242000, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173242500, 当前合约数: 1
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173242000, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173242500, 当前合约数: 2
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173242500, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173243000, 当前合约数: 1
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173242500, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173243000, 当前合约数: 2
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173243000, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173243500, 当前合约数: 1
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173243000, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173243500, 当前合约数: 2
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173243500, 水位线=-9223372036854775808
2025-08-20 10:56:05.619 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173244000, 当前合约数: 1
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173243500, 水位线=-9223372036854775808
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173244000, 当前合约数: 2
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173244000, 水位线=-9223372036854775808
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173244500, 当前合约数: 1
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173244000, 水位线=-9223372036854775808
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173244500, 当前合约数: 2
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173244500, 水位线=-9223372036854775808
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173245000, 当前合约数: 1
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173244500, 水位线=-9223372036854775808
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173245000, 当前合约数: 2
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173245000, 水位线=-9223372036854775808
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173245500, 当前合约数: 1
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173245000, 水位线=-9223372036854775808
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173245500, 当前合约数: 2
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173245500, 水位线=-9223372036854775808
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173246000, 当前合约数: 1
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173245500, 水位线=-9223372036854775808
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173246000, 当前合约数: 2
2025-08-20 10:56:05.620 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173246000, 水位线=-9223372036854775808
2025-08-20 10:56:05.621 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173246500, 当前合约数: 1
2025-08-20 10:56:05.621 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173246000, 水位线=-9223372036854775808
2025-08-20 10:56:05.621 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173246500, 当前合约数: 2
2025-08-20 10:56:05.621 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173237000, 触发时间=1742173237250, 水位线=1742173246418
2025-08-20 10:56:05.621 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173237000, 合约数=1
2025-08-20 10:56:05.621 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173237500, 触发时间=1742173237750, 水位线=1742173246418
2025-08-20 10:56:05.621 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173237500, 合约数=2
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173238000, 触发时间=1742173238250, 水位线=1742173246418
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173238000, 合约数=2
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173238500, 触发时间=1742173238750, 水位线=1742173246418
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173238500, 合约数=2
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173239000, 触发时间=1742173239250, 水位线=1742173246418
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173239000, 合约数=2
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173239500, 触发时间=1742173239750, 水位线=1742173246418
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173239500, 合约数=2
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173240000, 触发时间=1742173240250, 水位线=1742173246418
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173240000, 合约数=2
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173240500, 触发时间=1742173240750, 水位线=1742173246418
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173240500, 合约数=2
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173241000, 触发时间=1742173241250, 水位线=1742173246418
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173241000, 合约数=2
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173241500, 触发时间=1742173241750, 水位线=1742173246418
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173241500, 合约数=2
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173242000, 触发时间=1742173242250, 水位线=1742173246418
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173242000, 合约数=2
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173242500, 触发时间=1742173242750, 水位线=1742173246418
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173242500, 合约数=2
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173243000, 触发时间=1742173243250, 水位线=1742173246418
2025-08-20 10:56:05.622 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173243000, 合约数=2
2025-08-20 10:56:05.623 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173243500, 触发时间=1742173243750, 水位线=1742173246418
2025-08-20 10:56:05.623 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173243500, 合约数=2
2025-08-20 10:56:05.623 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173244000, 触发时间=1742173244250, 水位线=1742173246418
2025-08-20 10:56:05.623 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173244000, 合约数=2
2025-08-20 10:56:05.623 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173244500, 触发时间=1742173244750, 水位线=1742173246418
2025-08-20 10:56:05.623 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173244500, 合约数=2
2025-08-20 10:56:05.623 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173245000, 触发时间=1742173245250, 水位线=1742173246418
2025-08-20 10:56:05.623 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173245000, 合约数=2
2025-08-20 10:56:05.623 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173245500, 触发时间=1742173245750, 水位线=1742173246418
2025-08-20 10:56:05.623 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173245500, 合约数=2
2025-08-20 10:56:05.623 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173246000, 触发时间=1742173246250, 水位线=1742173246418
2025-08-20 10:56:05.623 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173246000, 合约数=2
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173246500
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173247000
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173246500
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173247000
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173247000
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173247500
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173247000
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.334 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173247500
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173247500
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173248000
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173247500
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173248000
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 EB2505 生成分层订单簿快照，时间戳=1742173247500
2025-08-20 10:56:06.335 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 EB2505 基础层处理: 6个单腿订单, 买盘3档, 卖盘3档
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 EB2505 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:632] - [分层订单簿] 合约 EB2505 输出快照: 基础层(3/3档) + 虚拟层(0/0档) = 最终(3/3档)
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 EB2505 注册下次定时器: 1742173248000
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 EB2505 生成分层订单簿快照，时间戳=1742173248000
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 EB2505 基础层处理: 6个单腿订单, 买盘3档, 卖盘3档
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 EB2505 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] INFO  [c.f.f.OrderBookReconstructionFunction:632] - [分层订单簿] 合约 EB2505 输出快照: 基础层(3/3档) + 虚拟层(0/0档) = 最终(3/3档)
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 EB2505 注册下次定时器: 1742173248500
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 JD2504-C-3200 生成分层订单簿快照，时间戳=1742173248000
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 JD2504-C-3200 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 JD2504-C-3200 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 JD2504-C-3200 订单簿为空，但仍输出空快照
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 JD2504-C-3200 注册下次定时器: 1742173248500
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:510] - 合约 CS2505-C-2750 生成分层订单簿快照，时间戳=1742173248000
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:577] - [基础层] 合约 CS2505-C-2750 基础层处理: 0个单腿订单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:604] - [虚拟层] 合约 CS2505-C-2750 虚拟层处理: 0个组合订单腿, 0个活跃虚拟挂单, 买盘0档, 卖盘0档
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:640] - [分层订单簿] 合约 CS2505-C-2750 订单簿为空，但仍输出空快照
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] DEBUG [c.f.f.OrderBookReconstructionFunction:656] - 合约 CS2505-C-2750 注册下次定时器: 1742173248500
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173246500, 水位线=1742173246418
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173247000, 当前合约数: 1
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173246500, 水位线=1742173246418
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173247000, 当前合约数: 2
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173247000, 水位线=1742173246418
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173247500, 当前合约数: 1
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173247000, 水位线=1742173246418
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173247500, 当前合约数: 2
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173247500, 水位线=1742173246418
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173248000, 当前合约数: 1
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173247500, 水位线=1742173246418
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173248000, 当前合约数: 2
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=EB2505, 事件时间=1742173247500, 水位线=1742173246418
2025-08-20 10:56:06.340 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173248000, 当前合约数: 3
2025-08-20 10:56:06.342 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:418] - 实时快照: 合约:EB2505 卖一:8038.00(1) 买一:8035.00(1)
2025-08-20 10:56:06.342 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=EB2505, 事件时间=1742173248000, 水位线=1742173246418
2025-08-20 10:56:06.342 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173248500, 当前合约数: 1
2025-08-20 10:56:06.342 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:418] - 实时快照: 合约:EB2505 卖一:8038.00(1) 买一:8035.00(1)
2025-08-20 10:56:06.342 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=JD2504-C-3200, 事件时间=1742173248000, 水位线=1742173246418
2025-08-20 10:56:06.342 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173248500, 当前合约数: 2
2025-08-20 10:56:06.342 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:381] - 接收快照: 合约=CS2505-C-2750, 事件时间=1742173248000, 水位线=1742173246418
2025-08-20 10:56:06.342 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:410] - 缓存快照到窗口: 1742173248500, 当前合约数: 3
2025-08-20 10:56:06.342 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173246500, 触发时间=1742173246750, 水位线=1742173248159
2025-08-20 10:56:06.342 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173246500, 合约数=2
2025-08-20 10:56:06.343 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173247000, 触发时间=1742173247250, 水位线=1742173248159
2025-08-20 10:56:06.343 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173247000, 合约数=2
2025-08-20 10:56:06.343 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] DEBUG [ORDERBOOK_SNAPSHOT:427] - 定时器触发: 窗口结束时间=1742173247500, 触发时间=1742173247750, 水位线=1742173248159
2025-08-20 10:56:06.343 [Kafka全局快照聚合器 -> Sink: Print to Std. Out (1/1)#0] INFO  [ORDERBOOK_SNAPSHOT:433] - 输出全局快照: 窗口时间=1742173247500, 合约数=2
