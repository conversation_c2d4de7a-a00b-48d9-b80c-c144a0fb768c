2025-08-20 10:33:02.761 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:33:02.779 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:33:02.780 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:33:03.284 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 10:33:03.393 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 10:33:03.393 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 10:33:03.456 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 10:33:03.457 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 10:33:03.576 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 10:33:03.576 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 10:33:03.576 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 10:33:03.577 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 10:33:03.580 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 10:33:03.725 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 10:33:03.725 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 10:33:03.725 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 10:33:03.725 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 10:33:03.726 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 10:33:03.864 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 10:33:03.865 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:33:03.869 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:33:03.869 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:04.340 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:56:04.357 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:56:04.357 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:56:04.825 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 10:56:04.939 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 10:56:04.939 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 10:56:05.008 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 10:56:05.008 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 10:56:05.117 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 10:56:05.117 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 10:56:05.117 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 10:56:05.118 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 10:56:05.120 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 10:56:05.277 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 10:56:05.277 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 10:56:05.277 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 10:56:05.278 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 10:56:05.278 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:05.420 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 10:59:50.238 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:59:50.255 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:59:50.255 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:59:50.712 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 10:59:50.828 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 10:59:50.828 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 10:59:50.888 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 10:59:50.888 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 10:59:50.994 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 10:59:50.994 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 10:59:50.994 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 10:59:50.995 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 10:59:50.998 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 10:59:51.146 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 10:59:51.146 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 10:59:51.146 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 10:59:51.147 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 10:59:51.147 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 10:59:51.289 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 10:59:51.290 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 10:59:51.290 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 10:59:51.291 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 10:59:51.291 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 10:59:51.291 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 10:59:51.291 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:59:51.291 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 10:59:51.291 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:59:51.293 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:59:51.293 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:59:51.295 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:59:51.295 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 10:59:51.295 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 10:59:51.296 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 10:59:51.296 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:59:51.297 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 11:01:28.221 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:01:28.238 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:01:28.238 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:01:28.675 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 11:01:28.789 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 11:01:28.790 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 11:01:28.848 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 11:01:28.848 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 11:01:28.954 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:01:28.954 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:01:28.954 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:01:28.955 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:01:28.957 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:01:29.108 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:01:29.108 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:01:29.108 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:01:29.109 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:01:29.109 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:01:29.250 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 11:01:29.250 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 11:01:29.250 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 11:01:29.251 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 11:01:29.251 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 11:01:29.251 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 11:01:29.251 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:01:29.251 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 11:01:29.251 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:01:29.252 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:01:29.253 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:01:29.254 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:01:29.255 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 11:01:29.255 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 11:01:29.255 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 11:01:29.256 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:01:29.256 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 11:02:24.106 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:02:24.122 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:02:24.123 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:02:24.566 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 11:02:24.683 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 11:02:24.683 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 11:02:24.741 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 11:02:24.741 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 11:02:24.860 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:02:24.860 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:02:24.860 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:02:24.861 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:02:24.865 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:02:25.010 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:02:25.010 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:02:25.010 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:02:25.010 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:02:25.011 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:02:25.148 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 11:02:25.148 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 11:02:25.149 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 11:02:25.149 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 11:02:25.149 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 11:02:25.149 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 11:02:25.149 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:02:25.149 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 11:02:25.150 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:02:25.151 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:02:25.151 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:02:25.153 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:02:25.153 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 11:02:25.153 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 11:02:25.154 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 11:02:25.154 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:02:25.154 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 11:05:28.515 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:05:28.531 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:05:28.532 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:05:28.984 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 11:05:29.097 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 11:05:29.097 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 11:05:29.154 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 11:05:29.154 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 11:05:29.258 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:05:29.258 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:05:29.258 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:05:29.259 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:05:29.262 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:05:29.414 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:05:29.414 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:05:29.414 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:05:29.414 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:05:29.415 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:05:29.550 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 11:05:29.550 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 11:05:29.551 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 11:05:29.552 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 11:05:29.552 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 11:05:29.552 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 11:05:29.553 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:05:29.553 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 11:05:29.553 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:05:29.554 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:05:29.555 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:24:09.806 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:24:09.825 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:24:09.826 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:24:10.384 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 11:24:10.502 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 11:24:10.502 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 11:24:10.564 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 11:24:10.565 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 11:24:10.670 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:24:10.670 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:24:10.670 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:24:10.671 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:24:10.673 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:24:10.828 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:24:10.828 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:24:10.828 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:24:10.828 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:24:10.828 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:24:10.966 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 11:24:10.966 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 11:24:10.967 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 11:24:10.967 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 11:24:10.967 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 11:24:10.968 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 11:24:10.968 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:24:10.968 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 11:24:10.968 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:24:10.970 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:24:10.970 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:54:45.782 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:54:45.800 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:54:45.801 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 11:54:46.276 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 11:54:46.390 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 11:54:46.390 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 11:54:46.449 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 11:54:46.449 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 11:54:46.555 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:54:46.555 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:54:46.555 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 11:54:46.556 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:54:46.559 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:54:46.704 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:54:46.704 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:54:46.704 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 11:54:46.704 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:54:46.704 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsNonBlocking(MailboxProcessor.java:399) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:361) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 11:54:46.843 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 11:54:46.844 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 11:54:46.844 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 11:54:46.845 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 11:54:46.845 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 11:54:46.845 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 11:54:46.845 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:54:46.845 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 11:54:46.845 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:54:46.847 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 11:54:46.847 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:02:21.972 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 12:02:21.988 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 12:02:21.989 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 12:02:22.437 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 12:02:22.551 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 12:02:22.551 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 12:02:22.613 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 12:02:22.614 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 12:02:22.719 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 12:02:22.719 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 12:02:22.719 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 12:02:22.720 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 12:02:22.723 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 12:02:22.877 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 12:02:22.877 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 12:02:22.877 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 12:02:22.878 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 12:02:22.878 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 12:02:23.012 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 12:02:23.012 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 12:02:23.013 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 12:02:23.013 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 12:02:23.014 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 12:02:23.014 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 12:02:23.014 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:02:23.014 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 12:02:23.014 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:02:23.015 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:02:23.016 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:04:02.341 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 12:04:02.357 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 12:04:02.358 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 12:04:02.872 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 12:04:02.993 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 12:04:02.993 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 12:04:03.065 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 12:04:03.065 [flink-pekko.actor.default-dispatcher-5] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 12:04:03.202 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 12:04:03.202 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 12:04:03.202 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 12:04:03.203 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 12:04:03.207 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 12:04:03.396 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 12:04:03.396 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 12:04:03.396 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 12:04:03.396 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 12:04:03.397 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 12:04:03.488 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 12:04:03.489 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 12:04:03.490 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 12:04:03.490 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 12:04:03.490 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 12:04:03.491 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 12:04:03.491 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:04:03.491 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 12:04:03.491 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:04:03.493 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:04:03.494 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:04:38.416 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.422 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.NetworkClient:1105] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Error while fetching metadata with correlation id 75 : {trade_data_event=LEADER_NOT_AVAILABLE}
2025-08-20 12:04:38.423 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.426 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.427 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.428 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.428 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.428 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.429 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.NetworkClient:1105] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Error while fetching metadata with correlation id 75 : {singleleg_order_data_event=LEADER_NOT_AVAILABLE}
2025-08-20 12:04:38.429 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.429 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.NetworkClient:1105] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Error while fetching metadata with correlation id 75 : {cmb_order_data_event=LEADER_NOT_AVAILABLE}
2025-08-20 12:04:38.429 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.429 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.430 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.431 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.431 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.431 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.432 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.432 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.432 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.432 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.432 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.432 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.433 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.433 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.433 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.433 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.433 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.434 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.434 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.434 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.434 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.435 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.435 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.435 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.436 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.436 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.436 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.437 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.437 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.437 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.438 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.438 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.439 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.439 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.439 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.439 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.440 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.440 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.440 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.442 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.442 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.442 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.443 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.443 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.443 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.444 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.445 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.445 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.445 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.446 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.446 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.447 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.447 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.447 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.447 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.448 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.448 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.448 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.449 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.449 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.449 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.455 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.456 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.456 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.456 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.456 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.456 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.456 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.457 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.457 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.457 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.458 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.458 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.458 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.458 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.459 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.459 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.459 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.459 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.460 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.460 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.460 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.461 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.461 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.461 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.461 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.461 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.462 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.462 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.462 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.462 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.463 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.463 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.463 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.463 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.463 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.464 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.464 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.464 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.464 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.464 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.465 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.465 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.465 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.465 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.465 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.466 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.466 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.466 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.466 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.466 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.467 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.467 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.467 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.467 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.467 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.468 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.468 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.468 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.468 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.468 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.468 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.469 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.469 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.469 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.475 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.475 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.476 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.476 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.476 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.477 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.477 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.477 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.477 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.477 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.477 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.478 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.478 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.478 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.478 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.478 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.479 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.479 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.479 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.479 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.479 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.479 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.480 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.480 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.480 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.480 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.480 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.480 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.480 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.480 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.481 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.481 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.481 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.481 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.481 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.481 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.481 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.481 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.482 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.482 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.482 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.482 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.482 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.482 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.483 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.483 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.483 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.483 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.483 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.483 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.483 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.484 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.484 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.484 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.484 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.484 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.484 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.484 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.485 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.485 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.485 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.485 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.485 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.485 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.485 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.485 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.486 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.486 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.486 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.486 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.486 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.486 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.486 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.486 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.486 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.487 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.487 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.487 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.487 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.487 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.487 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.487 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.488 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.488 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.488 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.488 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.488 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.488 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.488 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.488 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.489 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.489 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.489 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.489 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.489 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.489 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.489 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.489 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.489 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.490 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.490 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.490 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.490 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.490 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.490 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.490 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.490 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.490 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.491 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.491 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.491 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.491 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.491 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.491 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.491 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.491 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.491 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.492 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.492 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.492 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.492 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.492 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.492 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.492 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.493 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.493 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.493 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.493 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.493 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.493 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.493 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.493 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.493 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.494 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.494 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.494 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.494 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.494 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.494 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.494 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.494 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.494 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.494 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.494 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.495 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.495 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.495 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.495 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.495 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.495 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.495 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.495 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.495 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.496 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.496 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.496 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.496 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.496 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.496 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.496 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.496 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.496 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.496 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.497 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.497 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.497 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.497 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.497 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.497 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.497 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.497 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.497 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.497 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.498 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.498 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.498 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.498 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.498 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.498 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.498 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.498 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.499 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.499 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.499 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.499 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.499 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.499 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.499 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.499 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.500 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.500 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.500 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.500 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.500 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.500 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.501 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.501 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.501 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.501 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.501 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.501 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.501 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.501 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.502 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.502 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.502 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.502 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.502 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.502 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.502 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.502 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.502 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.502 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.502 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.503 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.503 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.503 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.503 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.503 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.503 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.503 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.504 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.504 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.504 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.504 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.504 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.504 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.504 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.504 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.504 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.504 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.505 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.505 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.505 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.505 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.505 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.505 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.505 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.506 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.506 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.506 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.506 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.506 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.506 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.506 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.506 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.506 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.506 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.507 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.507 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.507 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.507 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.507 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.507 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.507 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.507 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.508 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.508 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.508 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.508 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.508 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.508 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.508 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.508 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.508 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.509 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.509 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.509 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.509 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.509 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.509 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.509 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.509 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.509 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.509 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.510 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.510 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.510 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.510 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.510 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.510 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.510 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.510 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.510 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.511 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.511 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.511 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.511 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.511 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.511 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.511 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.511 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.511 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.511 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.511 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.511 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.512 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.512 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.512 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.512 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.512 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.512 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.512 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.512 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.512 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.512 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.513 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.513 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.513 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.513 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.513 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.513 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.513 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.513 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.513 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.514 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.514 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.514 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.514 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.514 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.514 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.515 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.515 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.516 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.516 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.516 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.517 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.517 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.517 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.517 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.517 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.518 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.518 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.518 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.518 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.519 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.519 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.519 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.519 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.520 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.520 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.520 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.520 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.521 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.521 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.521 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.521 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.521 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.522 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.522 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.523 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.523 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.523 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.524 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.524 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.525 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.525 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.526 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.526 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.526 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.526 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.527 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.527 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.527 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.527 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.528 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.528 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.528 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.529 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.529 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.531 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.NetworkClient:1105] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Error while fetching metadata with correlation id 245 : {trade_data_event=LEADER_NOT_AVAILABLE}
2025-08-20 12:04:38.531 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.532 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.532 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.533 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.533 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.534 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.534 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.535 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.536 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.537 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.NetworkClient:1105] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Error while fetching metadata with correlation id 246 : {cmb_order_data_event=LEADER_NOT_AVAILABLE}
2025-08-20 12:04:38.537 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.537 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.537 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.NetworkClient:1105] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Error while fetching metadata with correlation id 240 : {singleleg_order_data_event=LEADER_NOT_AVAILABLE}
2025-08-20 12:04:38.538 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.538 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.538 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.538 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.539 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.539 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.539 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.539 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.539 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.540 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.540 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.540 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.541 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.541 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.541 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.542 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.543 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.543 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.543 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.544 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.544 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.544 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.544 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.544 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.545 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.545 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.545 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.546 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.546 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.546 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.547 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.547 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.547 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.547 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.547 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.547 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.548 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.548 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.548 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.548 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.549 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.549 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.549 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.549 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.549 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.550 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.550 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.550 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.551 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.551 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.551 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.552 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.552 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.552 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.553 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.553 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.553 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.554 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.554 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.554 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.555 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.555 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.555 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.555 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.555 [Source Data Fetcher for Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition trade_data_event-0
2025-08-20 12:04:38.555 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.556 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.556 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.557 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.557 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.557 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.557 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.558 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.558 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.558 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.558 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.558 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.559 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.559 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.559 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.559 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.559 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.560 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.560 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.560 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.560 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.560 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.560 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.560 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.561 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.561 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.561 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.561 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.561 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.561 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.562 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.562 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.562 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.562 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.563 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.563 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.563 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.563 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.564 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.564 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.564 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.564 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.564 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.564 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.565 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.565 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.565 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.565 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.565 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.565 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.565 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.565 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.566 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.566 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.566 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.566 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.566 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.566 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:38.566 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.567 [Source Data Fetcher for Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition cmb_order_data_event-0
2025-08-20 12:04:38.567 [Source Data Fetcher for Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.i.Fetcher:1343] - [Consumer clientId=orderbook-local-test-group-0, groupId=orderbook-local-test-group] Received unknown topic or partition error in fetch for partition singleleg_order_data_event-0
2025-08-20 12:04:54.163 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:04:54.373 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 12:04:54.887 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 12:04:55.303 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 12:04:55.611 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:04:56.118 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 12:05:11.487 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 12:05:11.504 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 12:05:11.505 [main] WARN  [o.a.f.c.k.s.KafkaSourceBuilder:497] - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 12:05:11.938 [main] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:248] - No tokens obtained so skipping notifications
2025-08-20 12:05:12.038 [main] WARN  [o.a.f.r.w.WebMonitorUtils:76] - Log file environment variable 'log.file' is not set.
2025-08-20 12:05:12.038 [main] WARN  [o.a.f.r.w.WebMonitorUtils:82] - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 12:05:12.090 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:320] - No tokens obtained so skipping notifications
2025-08-20 12:05:12.090 [flink-pekko.actor.default-dispatcher-4] WARN  [o.a.f.r.s.t.DefaultDelegationTokenManager:335] - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 12:05:12.190 [SourceCoordinator-Source: Kafka-单腿订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 12:05:12.190 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 12:05:12.190 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.a.AdminClientConfig:385] - These configurations '[key.deserializer, value.deserializer, max.poll.records, client.id.prefix, group.id, partition.discovery.interval.ms, heartbeat.interval.ms, enable.auto.commit, fetch.max.wait.ms, fetch.min.bytes, session.timeout.ms, auto.offset.reset]' were supplied but are not used yet.
2025-08-20 12:05:12.191 [SourceCoordinator-Source: Kafka-组合订单] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 12:05:12.196 [SourceCoordinator-Source: Kafka-交易] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.admin.client:type=app-info,id=orderbook-local-test-group-enumerator-admin-client
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.<init>(KafkaAdminClient.java:602) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:544) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.KafkaAdminClient.createInternal(KafkaAdminClient.java:488) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.Admin.create(Admin.java:134) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.admin.AdminClient.create(AdminClient.java:39) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.getKafkaAdminClient(KafkaSourceEnumerator.java:441) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator.start(KafkaSourceEnumerator.java:164) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$start$1(SourceCoordinator.java:244) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.source.coordinator.SourceCoordinator.lambda$runInEventLoop$10(SourceCoordinator.java:533) ~[flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.util.ThrowableCatchingRunnable.run(ThrowableCatchingRunnable.java:40) [flink-core-1.20.0.jar:1.20.0]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) [?:?]
	at java.util.concurrent.FutureTask.run(FutureTask.java:264) [?:?]
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) [?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) [?:?]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 12:05:12.327 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 12:05:12.327 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 12:05:12.327 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.c.ConsumerConfig:385] - These configurations '[client.id.prefix, partition.discovery.interval.ms]' were supplied but are not used yet.
2025-08-20 12:05:12.328 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 12:05:12.328 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] WARN  [o.a.k.c.u.AppInfoParser:68] - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.consumer:type=app-info,id=orderbook-local-test-group-0
	at com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895) ~[?:?]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320) ~[?:?]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523) ~[?:?]
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:821) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:665) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:646) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.kafka.clients.consumer.KafkaConsumer.<init>(KafkaConsumer.java:626) ~[kafka-clients-3.4.0.jar:?]
	at org.apache.flink.connector.kafka.source.reader.KafkaPartitionSplitReader.<init>(KafkaPartitionSplitReader.java:97) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.kafka.source.KafkaSource.lambda$createReader$1(KafkaSource.java:185) ~[flink-connector-kafka-3.4.0-1.20.jar:3.4.0-1.20]
	at org.apache.flink.connector.base.source.reader.fetcher.SplitFetcherManager.createSplitFetcher(SplitFetcherManager.java:259) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.fetcher.SingleThreadFetcherManager.addSplits(SingleThreadFetcherManager.java:148) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.connector.base.source.reader.SourceReaderBase.addSplits(SourceReaderBase.java:315) ~[flink-connector-base-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleAddSplitsEvent(SourceOperator.java:606) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.api.operators.SourceOperator.handleOperatorEvent(SourceOperator.java:575) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.OperatorEventDispatcherImpl.dispatchEventToHandlers(OperatorEventDispatcherImpl.java:72) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.RegularOperatorChain.dispatchOperatorEvent(RegularOperatorChain.java:80) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.lambda$dispatchOperatorEvent$24(StreamTask.java:1609) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTaskActionExecutor$1.runThrowing(StreamTaskActionExecutor.java:50) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.Mail.run(Mail.java:101) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMail(MailboxProcessor.java:414) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMailsWhenDefaultActionUnavailable(MailboxProcessor.java:383) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.processMail(MailboxProcessor.java:368) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.mailbox.MailboxProcessor.runMailboxLoop(MailboxProcessor.java:229) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.runMailboxLoop(StreamTask.java:973) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.streaming.runtime.tasks.StreamTask.invoke(StreamTask.java:917) ~[flink-streaming-java-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.runWithSystemExitMonitoring(Task.java:970) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.restoreAndInvoke(Task.java:949) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.doRun(Task.java:763) [flink-runtime-1.20.0.jar:1.20.0]
	at org.apache.flink.runtime.taskmanager.Task.run(Task.java:575) [flink-runtime-1.20.0.jar:1.20.0]
	at java.lang.Thread.run(Thread.java:840) [?:?]
2025-08-20 12:05:12.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 12:05:12.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 12:05:12.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 12:05:12.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 12:05:12.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 12:05:12.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 12:05:12.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:05:12.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 12:05:12.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:05:12.421 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:05:12.421 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:05:12.423 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:05:12.423 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 12:05:12.424 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 12:05:12.424 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 12:05:12.425 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 12:05:12.425 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  [c.f.f.PnLCalculationFunction:180] - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
