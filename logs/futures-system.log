2025-08-20 10:33:02.713 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 10:33:02.715 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 使用本地测试环境配置: localhost:9092
2025-08-20 10:33:02.757 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 应用本地测试Kafka配置
2025-08-20 10:33:02.761 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:33:02.779 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:33:02.780 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:33:02.808 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 10:33:03.234 [flink-pekko.actor.default-dispatcher-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 10:33:03.268 [flink-metrics-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 10:33:03.284 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 10:33:03.393 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 10:33:03.393 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 10:33:03.456 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 10:33:03.457 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 10:33:03.577 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 10:33:03.580 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 10:33:03.580 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 10:33:03.692 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 10:33:03.692 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 10:33:03.692 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 10:33:03.698 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 10:33:03.698 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 10:33:03.698 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 10:33:03.863 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:33:03.864 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=184, 合约=JD2504-C-3200, 交易号=101355625, 开平=0, 买卖=B, 价格=4.5, 数量=5, 交易类型=0
2025-08-20 10:33:03.864 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=JD2504-C-3200, 方向=B, 价格=4.5, 数量=5, 时间戳=1742173236480
2025-08-20 10:33:03.864 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:33:03.864 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=148, 合约=JD2504-C-3200, 交易号=101355625, 开平=1, 买卖=S, 价格=4.5, 数量=5, 交易类型=0
2025-08-20 10:33:03.864 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 10:33:03.864 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=184, 合约=JD2504-C-3200, 交易号=101355626, 开平=0, 买卖=B, 价格=4.5, 数量=5, 交易类型=0
2025-08-20 10:33:03.865 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=JD2504-C-3200, 方向=B, 价格=4.5, 数量=5, 时间戳=1742173236480
2025-08-20 10:33:03.865 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=148, 合约=JD2504-C-3200, 交易号=101355626, 开平=1, 买卖=S, 价格=4.5, 数量=5, 交易类型=0
2025-08-20 10:33:03.865 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 10:33:03.865 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:33:03.865 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=173, 合约=CS2505-C-2750, 交易号=101355694, 开平=0, 买卖=B, 价格=13.0, 数量=1, 交易类型=0
2025-08-20 10:33:03.865 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=CS2505-C-2750, 方向=B, 价格=13.0, 数量=1, 时间戳=1742173236600
2025-08-20 10:33:03.865 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:33:03.865 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=110, 合约=CS2505-C-2750, 交易号=101355694, 开平=0, 买卖=S, 价格=13.0, 数量=1, 交易类型=0
2025-08-20 10:33:03.865 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=CS2505-C-2750, 方向=S, 价格=13.0, 数量=1, 时间戳=1742173236600
2025-08-20 10:33:03.865 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=101355700, 开平=0, 买卖=B, 价格=8035.0, 数量=10, 交易类型=0
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2505, 方向=B, 价格=8035.0, 数量=10, 时间戳=1742173247000
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=101, 合约=EB2505, 交易号=101355700, 开平=1, 买卖=B, 价格=8035.0, 数量=10, 交易类型=0
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=109, 合约=SH2505, 交易号=48060, 开平=0, 买卖=B, 价格=2690.0, 数量=1, 交易类型=0
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=SH2505, 方向=B, 价格=2690.0, 数量=1, 时间戳=1742173248000
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=7, 合约=SH2505, 交易号=48060, 开平=1, 买卖=S, 价格=2690.0, 数量=1, 交易类型=0
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 10:33:03.866 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.866 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=122, 合约=EB2505, 交易号=100052597, 开平=1, 买卖=B, 价格=8037.0, 数量=8, 交易类型=0
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 10:33:03.867 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=97, 合约=EB2505, 交易号=100052597, 开平=1, 买卖=S, 价格=8037.0, 数量=8, 交易类型=0
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=122, 合约=EB2505, 交易号=100052678, 开平=1, 买卖=B, 价格=8037.0, 数量=1, 交易类型=0
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=125, 合约=EB2505, 交易号=100052678, 开平=1, 买卖=S, 价格=8037.0, 数量=1, 交易类型=0
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=122, 合约=EB2505, 交易号=100052768, 开平=1, 买卖=B, 价格=8037.0, 数量=1, 交易类型=0
2025-08-20 10:33:03.867 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:33:03.868 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=100052768, 开平=1, 买卖=S, 价格=8037.0, 数量=1, 交易类型=2
2025-08-20 10:33:03.868 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=3, 剩余量=5, 时间戳=1742173236280
2025-08-20 10:33:03.868 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052768, 合约=EB2505, 会员=151
2025-08-20 10:33:03.868 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - FIFO匹配持仓: 开仓价=8035.0, 时间戳=1742173247000, 平仓价=8037.0, 数量=1, 盈亏=2.0
2025-08-20 10:33:03.869 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 结算会员=151 平仓盈亏=2.0, 累计盈亏=2.0, 当日已实现盈亏=2.0
2025-08-20 10:33:03.869 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=122, 合约=EB2505, 交易号=100052770, 开平=1, 买卖=B, 价格=8037.0, 数量=1, 交易类型=0
2025-08-20 10:33:03.869 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:33:03.869 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 目标订单10000000未成交在队列: 合约=JD2504-C-3200, 剩余量=5
2025-08-20 10:33:03.869 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=100052770, 开平=1, 买卖=S, 价格=8037.0, 数量=1, 交易类型=2
2025-08-20 10:33:03.869 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052770, 合约=EB2505, 会员=151
2025-08-20 10:33:03.869 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - FIFO匹配持仓: 开仓价=8035.0, 时间戳=1742173247000, 平仓价=8037.0, 数量=1, 盈亏=2.0
2025-08-20 10:33:03.869 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 结算会员=151 平仓盈亏=2.0, 累计盈亏=4.0, 当日已实现盈亏=4.0
2025-08-20 10:33:03.869 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=122, 合约=EB2505, 交易号=100052795, 开平=1, 买卖=B, 价格=8036.0, 数量=1, 交易类型=0
2025-08-20 10:33:03.869 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:33:03.870 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=100052795, 开平=1, 买卖=S, 价格=8036.0, 数量=1, 交易类型=2
2025-08-20 10:33:03.870 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052795, 合约=EB2505, 会员=151
2025-08-20 10:33:03.870 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - FIFO匹配持仓: 开仓价=8035.0, 时间戳=1742173247000, 平仓价=8036.0, 数量=1, 盈亏=1.0
2025-08-20 10:33:03.870 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 结算会员=151 平仓盈亏=1.0, 累计盈亏=5.0, 当日已实现盈亏=5.0
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000001, 合约=JD2504-C-3200
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.871 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000005, 合约=JD2504-C-3200
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000009, 合约=JD2504-C-3200
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.872 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000011, 合约=JD2504-C-3200
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000017, 合约=CS2505-C-2750
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000019, 合约=CS2505-C-2750
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.873 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=5, 剩余量=5, 时间戳=1742173239280
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 目标订单10000000已撤销，移除: 合约=JD2504-C-3200
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.874 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257007, 合约=EB2505
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257004, 合约=EB2505
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:33:03.875 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:33:03.876 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.876 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.876 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:33:03.876 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=3, 剩余量=13
2025-08-20 10:33:03.876 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 创建新的组合订单状态追踪: 订单号=100257414, 初始量=13
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=3, 剩余量=13
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=12
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第1轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=1, 完成度={:.1f}%
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=12
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=12
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=12
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=11
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第2轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=2, 完成度={:.1f}%
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=11
2025-08-20 10:33:03.877 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=11
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=11
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=10
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第3轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=3, 完成度={:.1f}%
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=10
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=10
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=10
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.878 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=9
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第4轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=4, 完成度={:.1f}%
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=9
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=9
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=9
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=8
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第5轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=5, 完成度={:.1f}%
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=8
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=8
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=8
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=7
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第6轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=6, 完成度={:.1f}%
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=7
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=7
2025-08-20 10:33:03.879 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=7
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=6
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第7轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=7, 完成度={:.1f}%
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=6
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=6
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=6
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=4
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第8轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=9, 完成度={:.1f}%
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=4
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=2
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第9轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=11, 完成度={:.1f}%
2025-08-20 10:33:03.880 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=2
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第10轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=12, 完成度={:.1f}%
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=0
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=0, 剩余量=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=0, 剩余量=1
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=4
2025-08-20 10:33:03.881 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 创建新的组合订单状态追踪: 订单号=100257400, 初始量=4
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=4
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257401, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 创建新的组合订单状态追踪: 订单号=100257401, 初始量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257401, 两条腿已在虚拟层处理
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第1轮成交: 订单号=100257400, 本轮成交量=2, 累计成交量=2, 完成度={:.1f}%
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:33:03.882 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:33:05.211 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [分层订单簿] 合约 EB2505 输出快照: 基础层(2/0档) + 虚拟层(0/0档) = 最终(2/0档)
2025-08-20 10:33:05.213 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [分层订单簿] 合约 EB2505 输出快照: 基础层(2/0档) + 虚拟层(0/0档) = 最终(2/0档)
2025-08-20 10:33:05.214 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [分层订单簿] 合约 EB2505 输出快照: 基础层(2/0档) + 虚拟层(0/0档) = 最终(2/0档)
2025-08-20 10:33:05.218 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [分层订单簿] 合约 EB2505 输出快照: 基础层(2/0档) + 虚拟层(0/0档) = 最终(2/0档)
2025-08-20 10:33:05.219 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [分层订单簿] 合约 EB2505 输出快照: 基础层(2/0档) + 虚拟层(0/0档) = 最终(2/0档)
2025-08-20 10:56:04.294 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 10:56:04.297 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 使用本地测试环境配置: localhost:9092
2025-08-20 10:56:04.336 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 应用本地测试Kafka配置
2025-08-20 10:56:04.340 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:56:04.357 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:56:04.357 [main] WARN  org.apache.flink.connector.kafka.source.KafkaSourceBuilder - Property auto.offset.reset is provided but will be overridden from earliest to earliest
2025-08-20 10:56:04.392 [main] INFO  com.futures.job.FuturesOrderBookKafkaJob - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 10:56:04.763 [flink-pekko.actor.default-dispatcher-4] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 10:56:04.805 [flink-metrics-5] INFO  org.apache.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-20 10:56:04.825 [main] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 10:56:04.939 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - Log file environment variable 'log.file' is not set.
2025-08-20 10:56:04.939 [main] WARN  org.apache.flink.runtime.webmonitor.WebMonitorUtils - JobManager log files are unavailable in the web dashboard. Log file location not found in environment variable 'log.file' or configuration key 'web.log.path'.
2025-08-20 10:56:05.008 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - No tokens obtained so skipping notifications
2025-08-20 10:56:05.008 [flink-pekko.actor.default-dispatcher-4] WARN  org.apache.flink.runtime.security.token.DefaultDelegationTokenManager - Tokens update task not started because either no tokens obtained or none of the tokens specified its renewal date
2025-08-20 10:56:05.118 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 10:56:05.120 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 10:56:05.121 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Starting the KafkaSourceEnumerator for consumer group orderbook-local-test-group without periodic partition discovery.
2025-08-20 10:56:05.244 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [singleleg_order_data_event-0]
2025-08-20 10:56:05.244 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [trade_data_event-0]
2025-08-20 10:56:05.244 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Discovered new partitions: [cmb_order_data_event-0]
2025-08-20 10:56:05.248 [SourceCoordinator-Source: Kafka-组合订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: cmb_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 10:56:05.248 [SourceCoordinator-Source: Kafka-交易] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: trade_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 10:56:05.248 [SourceCoordinator-Source: Kafka-单腿订单] INFO  org.apache.flink.connector.kafka.source.enumerator.KafkaSourceEnumerator - Assigning splits to readers {0=[[Partition: singleleg_order_data_event-0, StartingOffset: -2, StoppingOffset: -9223372036854775808]]}
2025-08-20 10:56:05.410 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.411 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.411 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.411 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=3, 剩余量=5, 时间戳=1742173236280
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=184, 合约=JD2504-C-3200, 交易号=101355625, 开平=0, 买卖=B, 价格=4.5, 数量=5, 交易类型=0
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=JD2504-C-3200, 方向=B, 价格=4.5, 数量=5, 时间戳=1742173236480
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=148, 合约=JD2504-C-3200, 交易号=101355625, 开平=1, 买卖=S, 价格=4.5, 数量=5, 交易类型=0
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355625, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=184, 合约=JD2504-C-3200, 交易号=101355626, 开平=0, 买卖=B, 价格=4.5, 数量=5, 交易类型=0
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=JD2504-C-3200, 方向=B, 价格=4.5, 数量=5, 时间戳=1742173236480
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=148, 合约=JD2504-C-3200, 交易号=101355626, 开平=1, 买卖=S, 价格=4.5, 数量=5, 交易类型=0
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355626, 合约=JD2504-C-3200, 方向=S, 数量=5
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=173, 合约=CS2505-C-2750, 交易号=101355694, 开平=0, 买卖=B, 价格=13.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.412 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=CS2505-C-2750, 方向=B, 价格=13.0, 数量=1, 时间戳=1742173236600
2025-08-20 10:56:05.412 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 目标订单10000000未成交在队列: 合约=JD2504-C-3200, 剩余量=5, 验证成功
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=110, 合约=CS2505-C-2750, 交易号=101355694, 开平=0, 买卖=S, 价格=13.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=CS2505-C-2750, 方向=S, 价格=13.0, 数量=1, 时间戳=1742173236600
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=101355700, 开平=0, 买卖=B, 价格=8035.0, 数量=10, 交易类型=0
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2505, 方向=B, 价格=8035.0, 数量=10, 时间戳=1742173247000
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=101, 合约=EB2505, 交易号=101355700, 开平=1, 买卖=B, 价格=8035.0, 数量=10, 交易类型=0
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=101355700, 合约=EB2505, 方向=B, 数量=10
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=109, 合约=SH2505, 交易号=48060, 开平=0, 买卖=B, 价格=2690.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=SH2505, 方向=B, 价格=2690.0, 数量=1, 时间戳=1742173248000
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=7, 合约=SH2505, 交易号=48060, 开平=1, 买卖=S, 价格=2690.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=48060, 合约=SH2505, 方向=S, 数量=1
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=122, 合约=EB2505, 交易号=100052597, 开平=1, 买卖=B, 价格=8037.0, 数量=8, 交易类型=0
2025-08-20 10:56:05.413 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.413 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=B, 数量=8
2025-08-20 10:56:05.413 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=97, 合约=EB2505, 交易号=100052597, 开平=1, 买卖=S, 价格=8037.0, 数量=8, 交易类型=0
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052597, 合约=EB2505, 方向=S, 数量=8
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000001, 合约=JD2504-C-3200
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=122, 合约=EB2505, 交易号=100052678, 开平=1, 买卖=B, 价格=8037.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=125, 合约=EB2505, 交易号=100052678, 开平=1, 买卖=S, 价格=8037.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052678, 合约=EB2505, 方向=S, 数量=1
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=122, 合约=EB2505, 交易号=100052768, 开平=1, 买卖=B, 价格=8037.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052768, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000005, 合约=JD2504-C-3200
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=100052768, 开平=1, 买卖=S, 价格=8037.0, 数量=1, 交易类型=2
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052768, 合约=EB2505, 会员=151
2025-08-20 10:56:05.414 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - FIFO匹配持仓: 开仓价=8035.0, 时间戳=1742173247000, 平仓价=8037.0, 数量=1, 盈亏=2.0
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.414 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000009, 合约=JD2504-C-3200
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000011, 合约=JD2504-C-3200
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.415 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000017, 合约=CS2505-C-2750
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 结算会员=151 平仓盈亏=2.0, 累计盈亏=2.0, 当日已实现盈亏=2.0
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=122, 合约=EB2505, 交易号=100052770, 开平=1, 买卖=B, 价格=8037.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052770, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=100052770, 开平=1, 买卖=S, 价格=8037.0, 数量=1, 交易类型=2
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052770, 合约=EB2505, 会员=151
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000019, 合约=CS2505-C-2750
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - FIFO匹配持仓: 开仓价=8035.0, 时间戳=1742173247000, 平仓价=8037.0, 数量=1, 盈亏=2.0
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 结算会员=151 平仓盈亏=2.0, 累计盈亏=4.0, 当日已实现盈亏=4.0
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=122, 合约=EB2505, 交易号=100052795, 开平=1, 买卖=B, 价格=8036.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.416 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=5, 剩余量=5, 时间戳=1742173239280
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052795, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=100052795, 开平=1, 买卖=S, 价格=8036.0, 数量=1, 交易类型=2
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052795, 合约=EB2505, 会员=151
2025-08-20 10:56:05.416 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - FIFO匹配持仓: 开仓价=8035.0, 时间戳=1742173247000, 平仓价=8036.0, 数量=1, 盈亏=1.0
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 结算会员=151 平仓盈亏=1.0, 累计盈亏=5.0, 当日已实现盈亏=5.0
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 目标订单10000000已撤销，移除: 合约=JD2504-C-3200, 验证成功
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2504, 交易号=100052796, 开平=0, 买卖=B, 价格=8094.0, 数量=1, 交易类型=2
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052796, 合约=EB2504, 会员=151
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2504, 方向=B, 价格=8094.0, 数量=1, 时间戳=1742173248326
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=149, 合约=EB2504, 交易号=100052796, 开平=0, 买卖=S, 价格=8094.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2504, 方向=S, 价格=8094.0, 数量=1, 时间戳=1742173248326
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=184, 合约=EB2504, 交易号=100052799, 开平=0, 买卖=S, 价格=8095.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2504, 方向=S, 价格=8095.0, 数量=1, 时间戳=1742173248344
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2504, 交易号=100052799, 开平=0, 买卖=B, 价格=8095.0, 数量=1, 交易类型=2
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052799, 合约=EB2504, 会员=151
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2504, 方向=B, 价格=8095.0, 数量=1, 时间戳=1742173248344
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=11, 合约=EB2505, 交易号=100052800, 开平=0, 买卖=B, 价格=8037.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2505, 方向=B, 价格=8037.0, 数量=1, 时间戳=1742173248344
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=100052800, 开平=1, 买卖=S, 价格=8037.0, 数量=1, 交易类型=2
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257007, 合约=EB2505
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052800, 合约=EB2505, 会员=151
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - FIFO匹配持仓: 开仓价=8035.0, 时间戳=1742173247000, 平仓价=8037.0, 数量=1, 盈亏=2.0
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.417 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 结算会员=151 平仓盈亏=2.0, 累计盈亏=7.0, 当日已实现盈亏=7.0
2025-08-20 10:56:05.417 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=21, 合约=EB2505, 交易号=100052821, 开平=1, 买卖=B, 价格=8036.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052821, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=100052821, 开平=1, 买卖=S, 价格=8036.0, 数量=1, 交易类型=2
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052821, 合约=EB2505, 会员=151
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257004, 合约=EB2505
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - FIFO匹配持仓: 开仓价=8035.0, 时间戳=1742173247000, 平仓价=8036.0, 数量=1, 盈亏=1.0
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 结算会员=151 平仓盈亏=1.0, 累计盈亏=8.0, 当日已实现盈亏=8.0
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=22, 合约=EB2505, 交易号=100052822, 开平=1, 买卖=B, 价格=8036.0, 数量=2, 交易类型=0
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052822, 合约=EB2505, 方向=B, 数量=2
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=100052822, 开平=1, 买卖=S, 价格=8036.0, 数量=2, 交易类型=2
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052822, 合约=EB2505, 会员=151
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=3, 剩余量=5, 时间戳=1742173236280
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - FIFO匹配持仓: 开仓价=8035.0, 时间戳=1742173247000, 平仓价=8036.0, 数量=2, 盈亏=2.0
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 目标订单10000000未成交在队列: 合约=JD2504-C-3200, 剩余量=5, 验证成功
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 结算会员=151 平仓盈亏=2.0, 累计盈亏=10.0, 当日已实现盈亏=10.0
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2504, 交易号=100052823, 开平=0, 买卖=B, 价格=8094.0, 数量=3, 交易类型=2
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052823, 合约=EB2504, 会员=151
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2504, 方向=B, 价格=8094.0, 数量=3, 时间戳=1742173248419
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=153, 合约=EB2504, 交易号=100052823, 开平=0, 买卖=S, 价格=8094.0, 数量=3, 交易类型=0
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2504, 方向=S, 价格=8094.0, 数量=3, 时间戳=1742173248419
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=22, 合约=EB2505, 交易号=100052833, 开平=1, 买卖=B, 价格=8036.0, 数量=2, 交易类型=0
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.418 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052833, 合约=EB2505, 方向=B, 数量=2
2025-08-20 10:56:05.418 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000001, 合约=JD2504-C-3200
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=100052833, 开平=1, 买卖=S, 价格=8036.0, 数量=2, 交易类型=2
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052833, 合约=EB2505, 会员=151
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - FIFO匹配持仓: 开仓价=8035.0, 时间戳=1742173247000, 平仓价=8036.0, 数量=2, 盈亏=2.0
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 结算会员=151 平仓盈亏=2.0, 累计盈亏=12.0, 当日已实现盈亏=12.0
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2504, 交易号=100052834, 开平=0, 买卖=B, 价格=8094.0, 数量=2, 交易类型=2
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052834, 合约=EB2504, 会员=151
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000005, 合约=JD2504-C-3200
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2504, 方向=B, 价格=8094.0, 数量=2, 时间戳=1742173248453
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=110, 合约=EB2504, 交易号=100052834, 开平=0, 买卖=S, 价格=8094.0, 数量=2, 交易类型=0
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2504, 方向=S, 价格=8094.0, 数量=2, 时间戳=1742173248453
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 首个交易日，无需结算
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=49, 合约=EB2504, 交易号=100052846, 开平=1, 买卖=B, 价格=8094.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052846, 合约=EB2504, 方向=B, 数量=1
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=49, 合约=EB2504, 交易号=100052846, 开平=0, 买卖=S, 价格=8094.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2504, 方向=S, 价格=8094.0, 数量=1, 时间戳=1742173248481
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000009, 合约=JD2504-C-3200
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=22, 合约=EB2505, 交易号=100052847, 开平=1, 买卖=B, 价格=8036.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052847, 合约=EB2505, 方向=B, 数量=1
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2505, 交易号=100052847, 开平=1, 买卖=S, 价格=8036.0, 数量=1, 交易类型=2
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052847, 合约=EB2505, 会员=151
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - FIFO匹配持仓: 开仓价=8035.0, 时间戳=1742173247000, 平仓价=8036.0, 数量=1, 盈亏=1.0
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 结算会员=151 平仓盈亏=1.0, 累计盈亏=13.0, 当日已实现盈亏=13.0
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000011, 合约=JD2504-C-3200
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2504, 交易号=100052848, 开平=0, 买卖=B, 价格=8094.0, 数量=1, 交易类型=2
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 组合委托成交，注意多轮成交情况: 交易号=100052848, 合约=EB2504, 会员=151
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2504, 方向=B, 价格=8094.0, 数量=1, 时间戳=1742173248481
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.419 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=49, 合约=EB2504, 交易号=100052848, 开平=0, 买卖=S, 价格=8094.0, 数量=1, 交易类型=0
2025-08-20 10:56:05.419 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.420 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2504, 方向=S, 价格=8094.0, 数量=1, 时间戳=1742173248481
2025-08-20 10:56:05.420 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=151, 合约=EB2504, 交易号=100052849, 开平=0, 买卖=B, 价格=8095.0, 数量=10, 交易类型=0
2025-08-20 10:56:05.420 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 开仓(FIFO): 合约=EB2504, 方向=B, 价格=8095.0, 数量=10, 时间戳=1742173248694
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.420 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] INFO  com.futures.function.PnLCalculationFunction - 处理交易: 结算会员=101, 合约=EB2504, 交易号=100052849, 开平=1, 买卖=S, 价格=8095.0, 数量=10, 交易类型=0
2025-08-20 10:56:05.420 [PnL计算 -> (Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out, Map -> Sink: Print to Std. Out) (1/1)#0] WARN  com.futures.function.PnLCalculationFunction - 发现无持仓的平仓交易 - 发送到异常流: 交易号=100052849, 合约=EB2504, 方向=S, 数量=10
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000017, 合约=CS2505-C-2750
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000019, 合约=CS2505-C-2750
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=5, 剩余量=5, 时间戳=1742173239280
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 目标订单10000000已撤销，移除: 合约=JD2504-C-3200, 验证成功
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.420 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257007, 合约=EB2505
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257004, 合约=EB2505
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000021, 合约=SH2505
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.421 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000023, 合约=SH2505
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.422 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257085, 合约=EB2505
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单部分成交，更新: 订单号=100256966, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单部分成交，更新: 订单号=100256966, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.423 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257259, 合约=EB2505
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100256966, 合约=EB2505
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100254367, 合约=EB2505
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257479, 合约=EB2504
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100256192, 合约=EB2504
2025-08-20 10:56:05.424 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257507, 合约=EB2505
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257466, 合约=EB2505
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=3, 剩余量=5, 时间戳=1742173236280
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 目标订单10000000未成交在队列: 合约=JD2504-C-3200, 剩余量=5, 验证成功
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000001, 合约=JD2504-C-3200
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.425 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000005, 合约=JD2504-C-3200
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000009, 合约=JD2504-C-3200
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000011, 合约=JD2504-C-3200
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000017, 合约=CS2505-C-2750
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000019, 合约=CS2505-C-2750
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.426 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=5, 剩余量=5, 时间戳=1742173239280
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 目标订单10000000已撤销，移除: 合约=JD2504-C-3200, 验证成功
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257007, 合约=EB2505
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257004, 合约=EB2505
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000021, 合约=SH2505
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.427 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000023, 合约=SH2505
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.428 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257085, 合约=EB2505
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单部分成交，更新: 订单号=100256966, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.429 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单部分成交，更新: 订单号=100256966, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257259, 合约=EB2505
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100256966, 合约=EB2505
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100254367, 合约=EB2505
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257479, 合约=EB2504
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.430 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100256192, 合约=EB2504
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257507, 合约=EB2505
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257466, 合约=EB2505
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=3, 剩余量=5, 时间戳=1742173236280
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 目标订单10000000未成交在队列: 合约=JD2504-C-3200, 剩余量=5, 验证成功
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000001, 合约=JD2504-C-3200
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.431 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000005, 合约=JD2504-C-3200
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000009, 合约=JD2504-C-3200
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000011, 合约=JD2504-C-3200
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000017, 合约=CS2505-C-2750
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000019, 合约=CS2505-C-2750
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.432 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=5, 剩余量=5, 时间戳=1742173239280
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 目标订单10000000已撤销，移除: 合约=JD2504-C-3200, 验证成功
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257007, 合约=EB2505
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257004, 合约=EB2505
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000021, 合约=SH2505
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.433 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000023, 合约=SH2505
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=3, 剩余量=5, 时间戳=1742173236280
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 目标订单10000000未成交在队列: 合约=JD2504-C-3200, 剩余量=5, 验证成功
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.434 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000001, 合约=JD2504-C-3200
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000005, 合约=JD2504-C-3200
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000009, 合约=JD2504-C-3200
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000011, 合约=JD2504-C-3200
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000017, 合约=CS2505-C-2750
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.435 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=10000019, 合约=CS2505-C-2750
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 处理目标订单10000000: 合约=JD2504-C-3200, 状态=5, 剩余量=5, 时间戳=1742173239280
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层-特别关注] 目标订单10000000已撤销，移除: 合约=JD2504-C-3200, 验证成功
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257007, 合约=EB2505
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [基础层] 单腿订单全部成交，移除: 订单号=100257004, 合约=EB2505
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=SingleLegOrderEvent
2025-08-20 10:56:05.436 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理基础层单腿订单事件
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=3, 剩余量=13
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 创建新的组合订单状态追踪: 订单号=100257414, 初始量=13
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=3, 剩余量=13
2025-08-20 10:56:05.437 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=12
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第1轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=1, 完成度={:.1f}%
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=12
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=12
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=12
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=11
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第2轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=2, 完成度={:.1f}%
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=11
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=11
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=11
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=10
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第3轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=3, 完成度={:.1f}%
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=10
2025-08-20 10:56:05.438 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=10
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=9
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第4轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=4, 完成度={:.1f}%
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=9
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=9
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=9
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=8
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第5轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=5, 完成度={:.1f}%
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=8
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=8
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=8
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=7
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第6轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=6, 完成度={:.1f}%
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=7
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=7
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=7
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=6
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第7轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=7, 完成度={:.1f}%
2025-08-20 10:56:05.439 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=6
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=6
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=6
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第8轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=9, 完成度={:.1f}%
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=2
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第9轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=11, 完成度={:.1f}%
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=2
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第10轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=12, 完成度={:.1f}%
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=0
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=0, 剩余量=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=0, 剩余量=1
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 创建新的组合订单状态追踪: 订单号=100257400, 初始量=4
2025-08-20 10:56:05.440 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=4
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257401, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 创建新的组合订单状态追踪: 订单号=100257401, 初始量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257401, 两条腿已在虚拟层处理
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第1轮成交: 订单号=100257400, 本轮成交量=2, 累计成交量=2, 完成度={:.1f}%
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=3, 剩余量=13
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=3, 剩余量=13
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=12
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第11轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=13, 完成度={:.1f}%
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=12
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=12
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=12
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=11
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第12轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=14, 完成度={:.1f}%
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=11
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=11
2025-08-20 10:56:05.441 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=11
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=10
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第13轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=15, 完成度={:.1f}%
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=10
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=10
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=9
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第14轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=16, 完成度={:.1f}%
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=9
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=9
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=9
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=8
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第15轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=17, 完成度={:.1f}%
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=8
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=8
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=8
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=7
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第16轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=18, 完成度={:.1f}%
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=7
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=7
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=7
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.442 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=6
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第17轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=19, 完成度={:.1f}%
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=6
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=6
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=6
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=4
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第18轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=21, 完成度={:.1f}%
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=4
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=2
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第19轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=23, 完成度={:.1f}%
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=2
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第20轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=24, 完成度={:.1f}%
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=0
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=0, 剩余量=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=0, 剩余量=1
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=4
2025-08-20 10:56:05.443 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=4
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257401, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257401, 两条腿已在虚拟层处理
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第2轮成交: 订单号=100257400, 本轮成交量=2, 累计成交量=4, 完成度={:.1f}%
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=3, 剩余量=13
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=3, 剩余量=13
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=12
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第21轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=25, 完成度={:.1f}%
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=12
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=12
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=12
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=11
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第22轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=26, 完成度={:.1f}%
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=11
2025-08-20 10:56:05.444 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=11
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=11
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=10
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第23轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=27, 完成度={:.1f}%
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=10
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=10
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=9
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第24轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=28, 完成度={:.1f}%
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=9
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=9
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=9
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=8
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第25轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=29, 完成度={:.1f}%
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=8
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=8
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=8
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=7
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第26轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=30, 完成度={:.1f}%
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=7
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=7
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=7
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=6
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第27轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=31, 完成度={:.1f}%
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=6
2025-08-20 10:56:05.445 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=6
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=6
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第28轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=33, 完成度={:.1f}%
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=2
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第29轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=35, 完成度={:.1f}%
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=2
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第30轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=36, 完成度={:.1f}%
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=0
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=0, 剩余量=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=0, 剩余量=1
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=4
2025-08-20 10:56:05.446 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257401, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257401, 两条腿已在虚拟层处理
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第3轮成交: 订单号=100257400, 本轮成交量=2, 累计成交量=6, 完成度={:.1f}%
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=3, 剩余量=13
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=3, 剩余量=13
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=12
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第31轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=37, 完成度={:.1f}%
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=12
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=12
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=12
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=11
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第32轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=38, 完成度={:.1f}%
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=11
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=11
2025-08-20 10:56:05.447 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=11
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=10
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第33轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=39, 完成度={:.1f}%
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=10
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=10
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=9
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第34轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=40, 完成度={:.1f}%
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=9
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=9
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=9
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=8
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第35轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=41, 完成度={:.1f}%
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=8
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=8
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=8
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=7
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第36轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=42, 完成度={:.1f}%
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=7
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=7
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=7
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=6
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第37轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=43, 完成度={:.1f}%
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=6
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=6
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=6
2025-08-20 10:56:05.448 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第38轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=45, 完成度={:.1f}%
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=2
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第39轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=47, 完成度={:.1f}%
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=2
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第40轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=48, 完成度={:.1f}%
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=0
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=0, 剩余量=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=0, 剩余量=1
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=4
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257401, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.449 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257401, 两条腿已在虚拟层处理
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第4轮成交: 订单号=100257400, 本轮成交量=2, 累计成交量=8, 完成度={:.1f}%
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=3, 剩余量=13
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=3, 剩余量=13
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=12
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第41轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=49, 完成度={:.1f}%
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=12
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=12
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=12
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=11
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第42轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=50, 完成度={:.1f}%
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=11
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=11
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=11
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=10
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第43轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=51, 完成度={:.1f}%
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=10
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=10
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=10
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.450 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=9
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第44轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=52, 完成度={:.1f}%
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=9
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=9
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=9
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=8
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第45轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=53, 完成度={:.1f}%
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=8
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=8
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=8
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=7
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第46轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=54, 完成度={:.1f}%
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=7
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=7
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=7
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=6
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第47轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=55, 完成度={:.1f}%
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=6
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=6
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=6
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=4
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第48轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=57, 完成度={:.1f}%
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=4
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=4
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=4
2025-08-20 10:56:05.451 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第49轮成交: 订单号=100257414, 本轮成交量=2, 累计成交量=59, 完成度={:.1f}%
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=1, 剩余量=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第50轮成交: 订单号=100257414, 本轮成交量=1, 累计成交量=60, 完成度={:.1f}%
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504, 剩余量=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=1, 剩余量=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿部分成交，更新虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505, 剩余量=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257414, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=0
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=1, 合约=EB2504, 状态=0, 剩余量=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=1, 合约=EB2504
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257414, 腿号=2, 合约=EB2505, 状态=0, 剩余量=1
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单腿全部成交，移除虚拟挂单: 订单号=100257414, 腿=2, 合约=EB2505
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257414, 两条腿已在虚拟层处理
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=4
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=4
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257401, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257401, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257401, 两条腿已在虚拟层处理
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 接收到事件: 类型=CombinationOrderEvent
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - 处理组合订单事件，将在内部拆分为虚拟层订单
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 处理组合订单: 订单号=100257400, 组合合约=SPD_EB2504_EB2505, 腿1=EB2504, 腿2=EB2505, 状态=3
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=1, 合约=EB2504, 状态=3, 剩余量=2
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 组合订单第5轮成交: 订单号=100257400, 本轮成交量=2, 累计成交量=10, 完成度={:.1f}%
2025-08-20 10:56:05.452 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [虚拟层] 处理组合订单腿: 订单号=100257400, 腿号=2, 合约=EB2505, 状态=3, 剩余量=2
2025-08-20 10:56:05.453 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [组合订单] 组合订单拆分完成: 订单号=100257400, 两条腿已在虚拟层处理
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [分层订单簿] 合约 EB2505 输出快照: 基础层(3/3档) + 虚拟层(0/0档) = 最终(3/3档)
2025-08-20 10:56:06.336 [订单簿重建 (1/1)#0] INFO  com.futures.function.OrderBookReconstructionFunction - [分层订单簿] 合约 EB2505 输出快照: 基础层(3/3档) + 虚拟层(0/0档) = 最终(3/3档)
